# ESP32 BluFi Flutter Application

一个完整的ESP32 BluFi协议Flutter实现，支持通过蓝牙配置ESP32设备的WiFi连接。

## 🚀 功能特性

### 核心功能
- ✅ **完整的BluFi协议支持** - 基于ESP-IDF v5.2.2规范
- ✅ **蓝牙设备扫描和连接** - 自动发现BluFi设备
- ✅ **安全加密通信** - DH密钥协商 + AES-128-CBC加密
- ✅ **WiFi网络配置** - 支持Station/SoftAP/混合模式
- ✅ **实时状态监控** - 连接状态、WiFi状态实时更新
- ✅ **错误处理和恢复** - 完善的错误提示和重试机制

### 高级功能
- ✅ **企业级WiFi支持** - WPA2-Enterprise认证
- ✅ **自定义数据传输** - 支持应用层自定义协议
- ✅ **设备版本查询** - 获取ESP32固件版本信息
- ✅ **WiFi网络扫描** - 扫描周围可用网络
- ✅ **多语言错误提示** - 中文本地化错误消息

## 📱 界面预览

### 主要页面
1. **设备扫描页面** - 扫描和连接BluFi设备
2. **WiFi配置页面** - 配置网络参数
3. **连接状态页面** - 监控设备和网络状态

## 🛠 技术架构

### 架构模式
- **MVVM架构** - 清晰的业务逻辑分离
- **Provider状态管理** - 响应式UI更新
- **分层设计** - 服务层、模型层、视图层
