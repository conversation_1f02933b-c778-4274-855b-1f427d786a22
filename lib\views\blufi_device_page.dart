import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/blufi_message_list.dart';
import '../models/blufi_device.dart';
import '../models/wifi_config.dart';
import '../models/connection_state.dart';
import '../services/blufi_service.dart';
import '../viewmodels/device_config_viewmodel.dart';
import 'configure_options_page.dart';
import 'dart:async';

/// BluFi设备页面
/// 完全模仿Android版本的BlufiActivity
class BluFiDevicePage extends StatefulWidget {
  final BluFiDevice device;

  const BluFiDevicePage({
    super.key,
    required this.device,
  });

  @override
  State<BluFiDevicePage> createState() => _BluFiDevicePageState();
}

class _BluFiDevicePageState extends State<BluFiDevicePage> {
  final BluFiMessageManager _messageManager = BluFiMessageManager();

  // BluFi服务实例
  BluFiService? _blufiService;

  // 连接状态
  bool _isConnected = false;
  bool _isConnecting = false;

  // 按钮状态管理 - 完全模仿Android版本
  bool _connectEnabled = true;
  bool _disconnectEnabled = false;
  bool _securityEnabled = false;
  bool _versionEnabled = false;
  bool _configureEnabled = false;
  bool _deviceScanEnabled = false;
  bool _deviceStatusEnabled = false;
  bool _customEnabled = false;

  // 订阅管理
  StreamSubscription? _stateSubscription;
  StreamSubscription? _errorSubscription;
  StreamSubscription? _wifiStateSubscription;
  StreamSubscription? _customDataSubscription;
  StreamSubscription? _versionResponseSubscription;
  StreamSubscription? _configureResponseSubscription;
  StreamSubscription? _securityResponseSubscription;

  @override
  void initState() {
    super.initState();
    _initializeBluFiService();
    _messageManager.addInfoMessage('Device: ${widget.device.name}');
    _messageManager
        .addInfoMessage('MAC: ${widget.device.bluetoothDevice.remoteId}');
  }

  @override
  void dispose() {
    _stateSubscription?.cancel();
    _errorSubscription?.cancel();
    _wifiStateSubscription?.cancel();
    _customDataSubscription?.cancel();
    _versionResponseSubscription?.cancel();
    _configureResponseSubscription?.cancel();
    _securityResponseSubscription?.cancel();
    _messageManager.dispose();
    super.dispose();
  }

  /// 初始化BluFi服务
  void _initializeBluFiService() {
    try {
      // 从Provider获取BluFiService实例
      final viewModel =
          Provider.of<DeviceConfigViewModel>(context, listen: false);
      _blufiService = viewModel.blufiService;

      if (_blufiService != null) {
        // 监听服务状态变化
        _stateSubscription =
            _blufiService!.stateStream.listen(_onServiceStateChanged);

        // 监听错误信息
        _errorSubscription = _blufiService!.errorStream.listen(_onServiceError);

        // 监听WiFi状态变化
        _wifiStateSubscription =
            _blufiService!.wifiStateStream.listen(_onWiFiStateChanged);

        // 监听自定义数据响应
        _customDataSubscription =
            _blufiService!.customDataStream.listen(_onCustomDataReceived);

        // 监听版本响应
        _versionResponseSubscription =
            _blufiService!.versionResponseStream.listen(_onVersionResponse);

        // 监听配置响应
        _configureResponseSubscription =
            _blufiService!.configureResponseStream.listen(_onConfigureResponse);

        // 监听安全协商响应
        _securityResponseSubscription =
            _blufiService!.securityResponseStream.listen(_onSecurityResponse);

        _messageManager.addInfoMessage('BluFi service initialized');
      } else {
        _messageManager.addErrorMessage('Failed to get BluFi service');
      }
    } catch (e) {
      _messageManager
          .addErrorMessage('BluFi service initialization failed: $e');
    }
  }

  /// 处理服务状态变化
  void _onServiceStateChanged(BluFiServiceState state) {
    _messageManager
        .addInfoMessage('Service state: ${state.toString().split('.').last}');

    switch (state) {
      case BluFiServiceState.ready:
        // 连接成功并准备就绪
        _onGattConnected();
        _onGattServiceCharacteristicDiscovered();
        break;
      case BluFiServiceState.idle:
        // 设备意外断开或主动断开，更新UI状态
        _onGattDisconnected();
        break;
      case BluFiServiceState.error:
        _onGattDisconnected();
        break;
      case BluFiServiceState.connecting:
        // 连接中状态，可以显示连接进度
        setState(() {
          _isConnecting = true;
          _connectEnabled = false;
        });
        break;
      default:
        break;
    }
  }

  /// 处理服务错误
  void _onServiceError(String error) {
    _messageManager.addErrorMessage(error);
  }

  /// 处理WiFi状态变化
  void _onWiFiStateChanged(dynamic wifiState) {
    if (wifiState is WiFiConnectionState) {
      // 格式化WiFi状态信息，模仿Android版本的显示格式
      final statusMessage = StringBuffer();
      statusMessage.writeln('Receive device status response:');
      statusMessage.writeln('Mode: ${wifiState.opModeDescription}');
      statusMessage.writeln('WiFi: ${wifiState.staStateDescription}');

      if (wifiState.staIp != null) {
        statusMessage.writeln('IP: ${wifiState.staIp}');
      }

      if (wifiState.staSsid != null) {
        statusMessage.writeln('SSID: ${wifiState.staSsid}');
      }

      if (wifiState.rssi != null) {
        statusMessage.writeln('RSSI: ${wifiState.rssi} dBm');
      }

      _messageManager.addNotificationMessage(statusMessage.toString().trim());
    } else {
      _messageManager.addNotificationMessage('WiFi state changed: $wifiState');
    }
  }

  /// 处理自定义数据响应
  void _onCustomDataReceived(String customData) {
    _messageManager.addNotificationMessage('Receive custom data:\n$customData');
  }

  /// 处理版本响应
  void _onVersionResponse(String version) {
    _messageManager
        .addNotificationMessage('Receive device version response:\n$version');
  }

  /// 处理配置响应
  void _onConfigureResponse(bool success) {
    if (success) {
      _messageManager.addNotificationMessage('Post configure params complete');
    } else {
      _messageManager.addErrorMessage('Post configure params error, code=-1');
    }
  }

  /// 处理安全协商响应
  void _onSecurityResponse(bool success) {
    if (success) {
      _messageManager.addNotificationMessage('Negotiate security complete');
    } else {
      _messageManager.addErrorMessage('Negotiate security error, code=-1');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _messageManager,
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.device.name),
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
        ),
        body: Column(
          children: [
            // BluFi功能按钮区域
            _buildFunctionButtons(),

            // 消息列表区域
            Expanded(
              child: Consumer<BluFiMessageManager>(
                builder: (context, manager, child) {
                  return BluFiMessageList(
                    messages: manager.messages,
                    onClear: manager.clearMessages,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 第一行：连接控制
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _connectEnabled ? _connect : null,
                  icon: const Icon(Icons.bluetooth_connected),
                  label: Text(_isConnecting ? 'Connecting...' : 'Connect'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _disconnectEnabled ? _disconnect : null,
                  icon: const Icon(Icons.bluetooth_disabled),
                  label: const Text('Disconnect'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 第二行：安全和版本
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _securityEnabled ? _negotiateSecurity : null,
                  icon: const Icon(Icons.security),
                  label: const Text('Security'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _versionEnabled ? _getVersion : null,
                  icon: const Icon(Icons.info),
                  label: const Text('Version'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 第三行：配置和扫描
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _configureEnabled ? _configure : null,
                  icon: const Icon(Icons.settings),
                  label: const Text('Configure'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _deviceScanEnabled ? _scanWifi : null,
                  icon: const Icon(Icons.wifi_find),
                  label: const Text('Device Scan'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 第四行：状态和自定义
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _deviceStatusEnabled ? _getDeviceStatus : null,
                  icon: const Icon(Icons.device_hub),
                  label: const Text('Device Status'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _customEnabled ? _sendCustomData : null,
                  icon: const Icon(Icons.send),
                  label: const Text('Custom'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.brown,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 连接设备 - 完全模仿Android版本
  void _connect() async {
    if (_blufiService == null) {
      _messageManager.addErrorMessage('BluFi service not available');
      return;
    }

    // 禁用连接按钮，防止重复点击
    setState(() {
      _connectEnabled = false;
      _isConnecting = true;
    });

    try {
      // 模仿Android版本：先断开现有连接，再重新连接
      if (_blufiService!.state != BluFiServiceState.idle) {
        _messageManager.addInfoMessage('Closing existing connection...');
        await _blufiService!.disconnect();
        // 短暂延迟确保断开完成
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // 使用真实的BluFi服务连接设备
      final success = await _blufiService!.connectToDevice(widget.device);

      if (success) {
        // 连接成功，状态会通过stateStream自动更新，不需要手动调用
        // _onGattConnected(); // 移除手动调用，避免重复
        _messageManager.addInfoMessage('Connection initiated successfully');
      } else {
        _messageManager.addErrorMessage('Connection failed');
        setState(() {
          _connectEnabled = true;
          _isConnecting = false;
        });
      }
    } catch (e) {
      _messageManager.addErrorMessage('Connection failed: $e');
      setState(() {
        _connectEnabled = true;
        _isConnecting = false;
      });
    }
  }

  // 模拟Android版本的onGattConnected
  void _onGattConnected() {
    setState(() {
      _isConnected = true;
      _isConnecting = false;
      _connectEnabled = false;
      _disconnectEnabled = true;
    });

    final deviceAddr = widget.device.bluetoothDevice.remoteId.toString();
    _messageManager.addInfoMessage('Connected $deviceAddr');
  }

  // 模拟Android版本的onGattServiceCharacteristicDiscovered
  void _onGattServiceCharacteristicDiscovered() {
    setState(() {
      _securityEnabled = true;
      _versionEnabled = true;
      _configureEnabled = true;
      _deviceStatusEnabled = true;
      _deviceScanEnabled = true;
      _customEnabled = true;
    });
  }

  // 断开连接 - 完全模仿Android版本
  void _disconnect() async {
    if (_blufiService == null) {
      _messageManager.addErrorMessage('BluFi service not available');
      return;
    }

    // 禁用断开按钮
    setState(() {
      _disconnectEnabled = false;
    });

    try {
      // 模仿Android版本：先发送断连请求，再断开连接
      await _blufiService!.disconnectBle();

      // 断开成功，状态会通过stateStream自动更新，不需要手动调用
      // _onGattDisconnected(); // 移除手动调用，避免重复
    } catch (e) {
      _messageManager.addErrorMessage('Disconnect failed: $e');
      // 如果断开失败，重新启用断开按钮
      setState(() {
        _disconnectEnabled = true;
      });
    }
  }

  // 模拟Android版本的onGattDisconnected
  void _onGattDisconnected() {
    print('_onGattDisconnected called - resetting all button states');

    setState(() {
      _isConnected = false;
      _isConnecting = false;
      _connectEnabled = true;
      _disconnectEnabled = false;
      _securityEnabled = false;
      _versionEnabled = false;
      _configureEnabled = false;
      _deviceStatusEnabled = false;
      _deviceScanEnabled = false;
      _customEnabled = false;
    });

    final deviceAddr = widget.device.bluetoothDevice.remoteId.toString();
    _messageManager.addInfoMessage('Disconnected $deviceAddr');

    print(
        'All buttons reset: Connect=$_connectEnabled, Disconnect=$_disconnectEnabled, Security=$_securityEnabled');
  }

  // 协商安全 - 完全模仿Android版本
  void _negotiateSecurity() async {
    if (_blufiService == null) {
      _messageManager.addErrorMessage('BluFi service not available');
      return;
    }

    // 禁用安全按钮
    setState(() {
      _securityEnabled = false;
    });

    try {
      // 使用真实的BluFi服务进行安全协商
      final success = await _blufiService!.negotiateSecurity();

      if (success) {
        _messageManager.addInfoMessage('Negotiate security complete');
      } else {
        _messageManager.addErrorMessage('Negotiate security failed, code=-1');
      }
    } catch (e) {
      _messageManager.addErrorMessage('Negotiate security failed, code=-1');
    } finally {
      // 重新启用安全按钮
      setState(() {
        _securityEnabled = _isConnected;
      });
    }
  }

  // 获取版本 - 完全模仿Android版本
  void _getVersion() async {
    if (_blufiService == null) {
      _messageManager.addErrorMessage('BluFi service not available');
      return;
    }

    // 禁用版本按钮
    setState(() {
      _versionEnabled = false;
    });

    try {
      // 使用真实的BluFi服务获取版本
      final version = await _blufiService!.queryDeviceVersion();

      if (version != null) {
        _messageManager
            .addNotificationMessage('Receive device version: $version');
      } else {
        _messageManager.addErrorMessage('Device version error, code=-1');
      }
    } catch (e) {
      _messageManager.addErrorMessage('Device version error, code=-1');
    } finally {
      // 重新启用版本按钮
      setState(() {
        _versionEnabled = _isConnected;
      });
    }
  }

  // 配置WiFi
  void _configure() async {
    final result = await Navigator.of(context).push<WiFiConfig>(
      MaterialPageRoute(
        builder: (context) => const ConfigureOptionsPage(),
      ),
    );

    if (result != null) {
      if (_blufiService == null) {
        _messageManager.addErrorMessage('BluFi service not available');
        return;
      }

      // 禁用配置按钮
      setState(() {
        _configureEnabled = false;
      });

      try {
        // 使用真实的BluFi服务配置WiFi
        final success = await _blufiService!.configureWiFi(result);

        if (success) {
          _messageManager.addInfoMessage('Post configure params complete');
        } else {
          _messageManager
              .addErrorMessage('Post configure params failed, code=-1');
        }
      } catch (e) {
        _messageManager
            .addErrorMessage('Post configure params failed, code=-1');
      } finally {
        // 重新启用配置按钮
        setState(() {
          _configureEnabled = _isConnected;
        });
      }
    }
  }

  // 扫描WiFi - 完全模仿Android版本
  void _scanWifi() async {
    if (_blufiService == null) {
      _messageManager.addErrorMessage('BluFi service not available');
      return;
    }

    // 禁用扫描按钮
    setState(() {
      _deviceScanEnabled = false;
    });

    try {
      // 使用真实的BluFi服务扫描WiFi
      final success = await _blufiService!.scanWiFiNetworks();

      if (!success) {
        _messageManager.addErrorMessage('Device scan result error, code=-1');
      }
      // 扫描结果会通过回调自动显示
    } catch (e) {
      _messageManager.addErrorMessage('Device scan result error, code=-1');
    } finally {
      // 重新启用扫描按钮
      setState(() {
        _deviceScanEnabled = _isConnected;
      });
    }
  }

  // 获取设备状态 - 完全模仿Android版本
  void _getDeviceStatus() async {
    if (_blufiService == null) {
      _messageManager.addErrorMessage('BluFi service not available');
      return;
    }

    // 禁用状态按钮
    setState(() {
      _deviceStatusEnabled = false;
    });

    try {
      // 使用真实的BluFi服务获取设备状态
      final success = await _blufiService!.getWiFiStatus();

      if (!success) {
        _messageManager
            .addErrorMessage('Device status response error, code=-1');
      }
      // 注意：真实的状态数据会通过wifiStateStream回调自动显示
      // 不应该在这里写死假数据
    } catch (e) {
      _messageManager.addErrorMessage('Device status response error, code=-1');
    } finally {
      // 重新启用状态按钮
      setState(() {
        _deviceStatusEnabled = _isConnected;
      });
    }
  }

  // 发送自定义数据
  void _sendCustomData() async {
    final controller = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Custom Data'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter custom data',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('Send'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      if (_blufiService == null) {
        _messageManager.addErrorMessage('BluFi service not available');
        return;
      }

      try {
        // 使用真实的BluFi服务发送自定义数据
        final customData = result.codeUnits; // 转换字符串为字节数组
        final success = await _blufiService!.sendCustomData(customData);

        if (success) {
          _messageManager.addInfoMessage('Post data $result complete');
        } else {
          _messageManager.addErrorMessage('Post data $result failed');
        }
      } catch (e) {
        _messageManager.addErrorMessage('Post data $result failed');
      }
    }
  }
}
