import 'package:flutter/material.dart';
import '../services/blufi_security_manager.dart';

/// 安全设置控件
/// 允许用户手动控制加密和校验设置
class SecuritySettingsWidget extends StatefulWidget {
  final BluFiSecurityManager securityManager;
  final VoidCallback? onSettingsChanged;

  const SecuritySettingsWidget({
    super.key,
    required this.securityManager,
    this.onSettingsChanged,
  });

  @override
  State<SecuritySettingsWidget> createState() => _SecuritySettingsWidgetState();
}

class _SecuritySettingsWidgetState extends State<SecuritySettingsWidget> {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '安全设置',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 加密设置
            SwitchListTile(
              title: const Text('启用数据加密'),
              subtitle: Text(
                widget.securityManager.isNegotiationComplete
                    ? '密钥协商已完成，可以使用加密'
                    : '需要先完成密钥协商',
              ),
              value: widget.securityManager.isEncryptionEnabled,
              onChanged: widget.securityManager.isNegotiationComplete
                  ? (value) {
                      setState(() {
                        widget.securityManager.setEncryptionEnabled(value);
                      });
                      widget.onSettingsChanged?.call();
                    }
                  : null,
              secondary: Icon(
                widget.securityManager.isEncryptionEnabled
                    ? Icons.lock
                    : Icons.lock_open,
                color: widget.securityManager.isEncryptionEnabled
                    ? Colors.green
                    : Colors.orange,
              ),
            ),
            
            // 校验设置
            SwitchListTile(
              title: const Text('启用数据校验'),
              subtitle: const Text('使用CRC16校验确保数据完整性'),
              value: widget.securityManager.isChecksumEnabled,
              onChanged: (value) {
                setState(() {
                  widget.securityManager.setChecksumEnabled(value);
                });
                widget.onSettingsChanged?.call();
              },
              secondary: Icon(
                widget.securityManager.isChecksumEnabled
                    ? Icons.verified
                    : Icons.warning,
                color: widget.securityManager.isChecksumEnabled
                    ? Colors.green
                    : Colors.orange,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 状态显示
            _buildStatusSection(),
            
            const SizedBox(height: 16),
            
            // 警告信息
            if (!widget.securityManager.isEncryptionEnabled)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  border: Border.all(color: Colors.orange),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '警告：未启用加密，WiFi密码将以明文传输',
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    final summary = widget.securityManager.getSecuritySummary();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '安全状态',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        _buildStatusItem(
          '协商状态',
          summary['state'].toString().split('.').last,
          _getStateColor(summary['state'].toString()),
        ),
        
        _buildStatusItem(
          '密钥协商',
          summary['negotiationComplete'] ? '已完成' : '未完成',
          summary['negotiationComplete'] ? Colors.green : Colors.orange,
        ),
        
        _buildStatusItem(
          '共享密钥',
          summary['hasSharedSecret'] ? '已生成' : '未生成',
          summary['hasSharedSecret'] ? Colors.green : Colors.red,
        ),
        
        _buildStatusItem(
          '有效加密',
          summary['effectiveEncryption'] ? '启用' : '禁用',
          summary['effectiveEncryption'] ? Colors.green : Colors.grey,
        ),
        
        _buildStatusItem(
          '有效校验',
          summary['effectiveChecksum'] ? '启用' : '禁用',
          summary['effectiveChecksum'] ? Colors.green : Colors.grey,
        ),
      ],
    );
  }

  Widget _buildStatusItem(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              border: Border.all(color: color),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStateColor(String state) {
    switch (state.toLowerCase()) {
      case 'ready':
        return Colors.green;
      case 'negotiating':
        return Colors.blue;
      case 'error':
        return Colors.red;
      case 'initialized':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

/// 安全设置对话框
class SecuritySettingsDialog extends StatelessWidget {
  final BluFiSecurityManager securityManager;

  const SecuritySettingsDialog({
    super.key,
    required this.securityManager,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('安全设置'),
      content: SizedBox(
        width: double.maxFinite,
        child: SecuritySettingsWidget(
          securityManager: securityManager,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('关闭'),
        ),
      ],
    );
  }

  /// 显示安全设置对话框
  static Future<void> show(
    BuildContext context,
    BluFiSecurityManager securityManager,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => SecuritySettingsDialog(
        securityManager: securityManager,
      ),
    );
  }
}
