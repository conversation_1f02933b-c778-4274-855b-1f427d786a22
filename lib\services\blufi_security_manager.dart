import 'dart:async';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'encryption_service.dart';

/// BluFi安全管理器
/// 根据ESP-IDF文档实现完整的安全协商和管理功能
/// 支持用户手动控制加密开关，优化了代码结构
class BluFiSecurityManager {
  static final Logger _logger = Logger();

  final EncryptionService _encryptionService = EncryptionService();

  // 安全配置状态
  bool _isNegotiationComplete = false;
  bool _userEnabledEncryption = false;
  bool _userEnabledChecksum = true;

  // 协商状态
  BluFiSecurityState _state = BluFiSecurityState.idle;
  final StreamController<BluFiSecurityState> _stateController =
      StreamController<BluFiSecurityState>.broadcast();

  // 错误流
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// 安全状态流
  Stream<BluFiSecurityState> get stateStream => _stateController.stream;

  /// 错误流
  Stream<String> get errorStream => _errorController.stream;

  /// 当前安全状态
  BluFiSecurityState get state => _state;

  /// 是否已完成密钥协商
  bool get isNegotiationComplete => _isNegotiationComplete;

  /// 用户是否启用了加密
  bool get isEncryptionEnabled =>
      _userEnabledEncryption && _isNegotiationComplete;

  /// 用户是否启用了校验
  bool get isChecksumEnabled => _userEnabledChecksum;

  /// 是否有共享密钥
  bool get hasSharedSecret => _encryptionService.hasSharedSecret;

  /// 初始化安全管理器
  Future<bool> initialize() async {
    try {
      _encryptionService.initializeDH();
      _updateState(BluFiSecurityState.initialized);
      _logger.i('BluFi security manager initialized');
      return true;
    } catch (e) {
      _logger.e('Failed to initialize security manager: $e');
      _updateState(BluFiSecurityState.error);
      _errorController.add('安全管理器初始化失败: $e');
      return false;
    }
  }

  /// 用户设置加密开关
  void setEncryptionEnabled(bool enabled) {
    _userEnabledEncryption = enabled;
    _logger.i('User ${enabled ? 'enabled' : 'disabled'} encryption');

    if (!enabled) {
      _logger.w(
          'Warning: Encryption disabled by user - data will be transmitted in plain text');
    }
  }

  /// 用户设置校验开关
  void setChecksumEnabled(bool enabled) {
    _userEnabledChecksum = enabled;
    _logger.i('User ${enabled ? 'enabled' : 'disabled'} checksum');
  }

  /// 开始密钥协商
  /// 注意：这个方法返回公钥数据，序列号由调用方管理
  Future<Uint8List?> startKeyNegotiation() async {
    if (!_encryptionService.isInitialized) {
      _errorController.add('加密服务未初始化');
      return null;
    }

    try {
      _updateState(BluFiSecurityState.negotiating);

      // 获取本地公钥
      final publicKeyBytes = _encryptionService.getPublicKeyBytes();
      if (publicKeyBytes == null) {
        _errorController.add('无法获取公钥');
        _updateState(BluFiSecurityState.error);
        return null;
      }

      _logger.i(
          'Starting key negotiation, public key length: ${publicKeyBytes.length}');
      _logger.d(
          'Public key: ${publicKeyBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}');

      // 直接返回公钥数据，让调用方创建帧并管理序列号
      return publicKeyBytes;
    } catch (e) {
      _logger.e('Error starting key negotiation: $e');
      _errorController.add('密钥协商启动失败: $e');
      _updateState(BluFiSecurityState.error);
      return null;
    }
  }

  /// 处理接收到的协商数据
  Future<bool> handleNegotiationData(Uint8List data) async {
    if (_state != BluFiSecurityState.negotiating) {
      _logger.w('Received negotiation data in wrong state: $_state');
      return false;
    }

    try {
      _logger.i('Processing negotiation data, length: ${data.length}');

      // 根据ESP-IDF文档，协商数据应该是对方的公钥
      if (data.length < 128) {
        _logger.e(
            'Invalid negotiation data length: ${data.length}, expected >= 128');
        _errorController.add('协商数据长度无效');
        _updateState(BluFiSecurityState.error);
        return false;
      }

      // 提取对方公钥（前128字节）
      final peerPublicKey = data.sublist(0, 128);
      _logger.d(
          'Peer public key: ${peerPublicKey.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}');

      // 计算共享密钥
      if (_encryptionService.computeSharedSecret(peerPublicKey)) {
        _isNegotiationComplete = true;
        _updateState(BluFiSecurityState.ready);
        _logger.i('Key negotiation completed successfully');
        return true;
      } else {
        _logger.e('Failed to compute shared secret');
        _errorController.add('共享密钥计算失败');
        _updateState(BluFiSecurityState.error);
        return false;
      }
    } catch (e) {
      _logger.e('Error handling negotiation data: $e');
      _errorController.add('协商数据处理失败: $e');
      _updateState(BluFiSecurityState.error);
      return false;
    }
  }

  /// 获取安全模式配置
  /// 返回安全模式的配置信息，供帧解析器使用
  Map<String, bool> getSecurityModeConfig() {
    return {
      'controlFrameChecksum': true, // 控制帧总是启用校验
      'controlFrameEncrypt': false, // 控制帧不加密
      'dataFrameChecksum': _userEnabledChecksum,
      'dataFrameEncrypt': isEncryptionEnabled,
    };
  }

  /// 加密数据（如果启用）
  Uint8List? encryptData(Uint8List data, int sequence) {
    // 如果用户未启用加密或协商未完成，直接返回原数据
    if (!isEncryptionEnabled) {
      _logger.d('Encryption disabled, returning plain data');
      return data;
    }

    if (!_encryptionService.hasSharedSecret) {
      _logger.e('Cannot encrypt: no shared secret available');
      _errorController.add('加密失败：缺少共享密钥');
      return null;
    }

    final encrypted = _encryptionService.encrypt(data, sequence);
    if (encrypted == null) {
      _logger.e('Encryption operation failed');
      _errorController.add('数据加密失败');
      return null;
    }

    _logger.d('Successfully encrypted ${data.length} bytes');
    return encrypted;
  }

  /// 解密数据（如果需要）
  Uint8List? decryptData(Uint8List data, int sequence,
      {bool forceDecrypt = false}) {
    // 如果没有共享密钥且不强制解密，假设数据未加密
    if (!_encryptionService.hasSharedSecret && !forceDecrypt) {
      _logger.d('No shared secret, assuming plain data');
      return data;
    }

    if (!_encryptionService.hasSharedSecret) {
      _logger.e('Cannot decrypt: no shared secret available');
      _errorController.add('解密失败：缺少共享密钥');
      return null;
    }

    final decrypted = _encryptionService.decrypt(data, sequence);
    if (decrypted == null) {
      _logger.e('Decryption operation failed');
      _errorController.add('数据解密失败');
      return null;
    }

    _logger.d('Successfully decrypted ${data.length} bytes');
    return decrypted;
  }

  /// 计算校验和（如果启用）
  int? calculateChecksum(Uint8List data, int sequence) {
    if (!_userEnabledChecksum) {
      return null;
    }

    return _encryptionService.calculateCRC16(data);
  }

  /// 验证校验和
  bool verifyChecksum(Uint8List data, int sequence, int expectedChecksum) {
    if (!_userEnabledChecksum) {
      return true; // 如果未启用校验，总是返回true
    }

    return _encryptionService.verifyCRC16(data, expectedChecksum);
  }

  /// 重置安全状态
  void reset() {
    _isNegotiationComplete = false;
    _encryptionService.reset();
    _updateState(BluFiSecurityState.idle);
    _logger.i('Security manager reset');
  }

  /// 更新状态
  void _updateState(BluFiSecurityState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(newState);
      _logger.d('Security state changed to: $newState');
    }
  }

  /// 获取安全配置摘要
  Map<String, dynamic> getSecuritySummary() {
    return {
      'state': _state.toString(),
      'negotiationComplete': _isNegotiationComplete,
      'userEncryptionEnabled': _userEnabledEncryption,
      'userChecksumEnabled': _userEnabledChecksum,
      'effectiveEncryption': isEncryptionEnabled,
      'effectiveChecksum': isChecksumEnabled,
      'hasSharedSecret': hasSharedSecret,
    };
  }

  /// 释放资源
  void dispose() {
    _stateController.close();
    _errorController.close();
  }
}

/// BluFi安全状态
enum BluFiSecurityState {
  idle, // 空闲状态
  initialized, // 已初始化
  negotiating, // 正在协商
  ready, // 协商完成，可以使用
  error, // 错误状态
}
