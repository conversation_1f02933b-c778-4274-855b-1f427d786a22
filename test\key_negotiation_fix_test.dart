import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:esp32_blufi/services/blufi_security_manager.dart';
import 'package:esp32_blufi/services/blufi_protocol.dart';

void main() {
  group('Key Negotiation Fix Tests', () {
    late BluFiSecurityManager securityManager;
    late BluFiProtocolService protocolService;

    setUp(() {
      securityManager = BluFiSecurityManager();
      protocolService = BluFiProtocolService();
    });

    test('Security manager should be initialized after protocol initialization', () async {
      // 初始化协议服务（应该同时初始化安全管理器）
      final result = await protocolService.initialize();
      expect(result, isTrue);
      
      // 检查安全管理器状态
      expect(protocolService.securityManager.state, equals(BluFiSecurityState.initialized));
      
      // 应该能够开始密钥协商
      final negotiationFrames = await protocolService.startKeyNegotiation();
      expect(negotiationFrames, isNotNull);
      expect(negotiationFrames.length, equals(1));
      expect(negotiationFrames.first.length, greaterThan(0));
    });

    test('Security manager should be reinitialized after reset', () async {
      // 初始化
      await protocolService.initialize();
      expect(protocolService.securityManager.state, equals(BluFiSecurityState.initialized));
      
      // 重置
      protocolService.reset();
      expect(protocolService.securityManager.state, equals(BluFiSecurityState.idle));
      
      // 重新初始化
      final result = await protocolService.initialize();
      expect(result, isTrue);
      expect(protocolService.securityManager.state, equals(BluFiSecurityState.initialized));
      
      // 应该能够开始密钥协商
      final negotiationFrames = await protocolService.startKeyNegotiation();
      expect(negotiationFrames, isNotNull);
      expect(negotiationFrames.length, equals(1));
    });

    test('Direct security manager should work after initialization', () async {
      // 直接初始化安全管理器
      final result = await securityManager.initialize();
      expect(result, isTrue);
      expect(securityManager.state, equals(BluFiSecurityState.initialized));
      
      // 应该能够开始密钥协商
      final negotiationData = await securityManager.startKeyNegotiation();
      expect(negotiationData, isNotNull);
      expect(negotiationData!.length, greaterThan(0));
    });

    test('Security manager should fail gracefully when not initialized', () async {
      // 不初始化，直接尝试密钥协商
      final negotiationData = await securityManager.startKeyNegotiation();
      expect(negotiationData, isNull);
      expect(securityManager.state, equals(BluFiSecurityState.idle));
    });

    test('Protocol service should handle reset and reinitialize correctly', () async {
      // 初始化
      await protocolService.initialize();
      
      // 验证可以进行密钥协商
      var negotiationFrames = await protocolService.startKeyNegotiation();
      expect(negotiationFrames, isNotNull);
      
      // 重置
      protocolService.reset();
      
      // 重置后应该无法进行密钥协商
      try {
        await protocolService.startKeyNegotiation();
        fail('Should throw StateError');
      } catch (e) {
        expect(e, isA<StateError>());
        expect(e.toString(), contains('Failed to start key negotiation'));
      }
      
      // 重新初始化
      await protocolService.initialize();
      
      // 现在应该可以进行密钥协商
      negotiationFrames = await protocolService.startKeyNegotiation();
      expect(negotiationFrames, isNotNull);
      expect(negotiationFrames.length, equals(1));
    });

    test('Multiple reset and initialize cycles should work', () async {
      for (int i = 0; i < 3; i++) {
        // 初始化
        final initResult = await protocolService.initialize();
        expect(initResult, isTrue, reason: 'Initialization $i should succeed');
        
        // 验证密钥协商
        final negotiationFrames = await protocolService.startKeyNegotiation();
        expect(negotiationFrames, isNotNull, reason: 'Key negotiation $i should work');
        expect(negotiationFrames.length, equals(1));
        
        // 重置
        protocolService.reset();
        expect(protocolService.securityManager.state, equals(BluFiSecurityState.idle));
      }
    });

    test('Security manager state transitions should be correct', () async {
      // 初始状态
      expect(securityManager.state, equals(BluFiSecurityState.idle));
      
      // 初始化后
      await securityManager.initialize();
      expect(securityManager.state, equals(BluFiSecurityState.initialized));
      
      // 开始协商后
      await securityManager.startKeyNegotiation();
      expect(securityManager.state, equals(BluFiSecurityState.negotiating));
      
      // 重置后
      securityManager.reset();
      expect(securityManager.state, equals(BluFiSecurityState.idle));
    });

    tearDown(() {
      protocolService.dispose();
      securityManager.dispose();
    });
  });
}
