import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:esp32_blufi/services/encryption_service.dart';
import 'package:esp32_blufi/services/blufi_security_manager.dart';

void main() {
  group('BluFi Encryption Tests', () {
    late EncryptionService encryptionService;
    late BluFiSecurityManager securityManager;

    setUp(() {
      encryptionService = EncryptionService();
      securityManager = BluFiSecurityManager();
    });

    test('DH key generation should work', () {
      encryptionService.initializeDH();

      expect(encryptionService.isInitialized, isTrue);

      final publicKey = encryptionService.getPublicKeyBytes();
      expect(publicKey, isNotNull);
      expect(publicKey!.length, equals(128)); // 1024位 = 128字节
    });

    test('Shared secret computation should work', () {
      // 初始化两个加密服务实例模拟DH密钥交换
      final service1 = EncryptionService();
      final service2 = EncryptionService();

      service1.initializeDH();
      service2.initializeDH();

      final publicKey1 = service1.getPublicKeyBytes()!;
      final publicKey2 = service2.getPublicKeyBytes()!;

      // 交换公钥并计算共享密钥
      final result1 = service1.computeSharedSecret(publicKey2);
      final result2 = service2.computeSharedSecret(publicKey1);

      expect(result1, isTrue);
      expect(result2, isTrue);
      expect(service1.hasSharedSecret, isTrue);
      expect(service2.hasSharedSecret, isTrue);
    });

    test('AES encryption/decryption should work', () {
      // 设置加密服务
      encryptionService.initializeDH();
      final publicKey = encryptionService.getPublicKeyBytes()!;

      // 模拟与自己进行密钥交换（测试用）
      encryptionService.computeSharedSecret(publicKey);

      // 测试数据
      final testData = Uint8List.fromList('Hello BluFi!'.codeUnits);
      const sequence = 1;

      // 加密
      final encrypted = encryptionService.encrypt(testData, sequence);
      expect(encrypted, isNotNull);
      expect(encrypted!.length, greaterThan(testData.length)); // 由于填充

      // 解密
      final decrypted = encryptionService.decrypt(encrypted, sequence);
      expect(decrypted, isNotNull);
      expect(decrypted, equals(testData));
    });

    test('Security manager initialization should work', () async {
      final result = await securityManager.initialize();
      expect(result, isTrue);
      expect(securityManager.state, equals(BluFiSecurityState.initialized));
    });

    test('Security manager encryption control should work', () async {
      await securityManager.initialize();

      // 默认状态
      expect(securityManager.isEncryptionEnabled, isFalse);

      // 启用加密（但没有协商完成）
      securityManager.setEncryptionEnabled(true);
      expect(securityManager.isEncryptionEnabled, isFalse); // 仍然false，因为协商未完成

      // 模拟协商完成
      final negotiationData = await securityManager.startKeyNegotiation();
      expect(negotiationData, isNotNull);

      // 模拟接收到对方公钥（使用自己的公钥测试）
      final publicKey = Uint8List(128);
      for (int i = 0; i < 128; i++) {
        publicKey[i] = i % 256;
      }

      final negotiationResult =
          await securityManager.handleNegotiationData(publicKey);
      expect(negotiationResult, isTrue);
      expect(securityManager.isNegotiationComplete, isTrue);
      expect(securityManager.isEncryptionEnabled, isTrue); // 现在应该是true
    });

    test('CRC16 calculation should work', () {
      final testData = Uint8List.fromList('test'.codeUnits);
      final crc = encryptionService.calculateCRC16(testData);

      expect(crc, isA<int>());
      expect(crc, greaterThan(0));
      expect(crc, lessThan(65536)); // 16位值

      // 验证CRC
      final isValid = encryptionService.verifyCRC16(testData, crc);
      expect(isValid, isTrue);

      // 验证错误的CRC
      final isInvalid = encryptionService.verifyCRC16(testData, crc + 1);
      expect(isInvalid, isFalse);
    });

    test('Different sequences should produce different IVs', () {
      encryptionService.initializeDH();
      final publicKey = encryptionService.getPublicKeyBytes()!;
      encryptionService.computeSharedSecret(publicKey);

      final testData = Uint8List.fromList('test data'.codeUnits);

      final encrypted1 = encryptionService.encrypt(testData, 1);
      final encrypted2 = encryptionService.encrypt(testData, 2);

      expect(encrypted1, isNotNull);
      expect(encrypted2, isNotNull);
      expect(encrypted1, isNot(equals(encrypted2))); // 不同序列号应产生不同密文
    });

    test('Security mode configuration should work', () {
      securityManager.setEncryptionEnabled(true);
      securityManager.setChecksumEnabled(true);

      final config = securityManager.getSecurityModeConfig();
      expect(config, isNotNull);
      expect(config['controlFrameChecksum'], isTrue);
      expect(config['controlFrameEncrypt'], isFalse);
      expect(config['dataFrameChecksum'], isTrue);
      expect(config['dataFrameEncrypt'], isFalse); // 因为没有完成协商
    });

    tearDown(() {
      encryptionService.reset();
      securityManager.reset();
    });
  });
}
