import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:esp32_blufi/services/blufi_security_manager.dart';

void main() {
  group('BluFi Integration Tests', () {
    test('Complete encryption workflow should work', () async {
      final securityManager = BluFiSecurityManager();

      try {
        // 1. 初始化
        final initResult = await securityManager.initialize();
        expect(initResult, isTrue);
        expect(securityManager.state, equals(BluFiSecurityState.initialized));

        // 2. 配置加密
        securityManager.setEncryptionEnabled(true);
        securityManager.setChecksumEnabled(true);
        expect(securityManager.isChecksumEnabled, isTrue);

        // 3. 开始密钥协商
        final negotiationData = await securityManager.startKeyNegotiation();
        expect(negotiationData, isNotNull);
        expect(negotiationData!.length, greaterThan(0));

        // 4. 模拟密钥交换
        final mockPeerPublicKey = Uint8List(128);
        for (int i = 0; i < 128; i++) {
          mockPeerPublicKey[i] = (i * 7 + 13) % 256;
        }

        final negotiationResult =
            await securityManager.handleNegotiationData(mockPeerPublicKey);
        expect(negotiationResult, isTrue);
        expect(securityManager.isNegotiationComplete, isTrue);
        expect(securityManager.isEncryptionEnabled, isTrue);

        // 5. 验证安全模式配置
        final securityConfig = securityManager.getSecurityModeConfig();
        expect(securityConfig, isNotNull);
        expect(securityConfig['dataFrameEncrypt'], isTrue); // 现在应该是true

        // 6. 测试数据加密/解密
        final testData = Uint8List.fromList('Hello ESP32 BluFi!'.codeUnits);

        final encryptedData = securityManager.encryptData(testData, 1);
        expect(encryptedData, isNotNull);
        expect(encryptedData!.length, greaterThan(testData.length));

        final decryptedData = securityManager.decryptData(encryptedData, 1);
        expect(decryptedData, isNotNull);
        expect(decryptedData, equals(testData));

        // 7. 测试校验和
        final checksum = securityManager.calculateChecksum(testData, 1);
        expect(checksum, isNotNull);

        final isChecksumValid =
            securityManager.verifyChecksum(testData, 1, checksum!);
        expect(isChecksumValid, isTrue);

        // 8. 测试不同序列号产生不同密文
        final encryptedData2 = securityManager.encryptData(testData, 2);
        expect(encryptedData2, isNotNull);
        expect(encryptedData2, isNot(equals(encryptedData)));

        // 9. 验证安全配置
        final summary = securityManager.getSecuritySummary();
        expect(summary['negotiationComplete'], isTrue);
        expect(summary['effectiveEncryption'], isTrue);
        expect(summary['effectiveChecksum'], isTrue);

        print('✅ 完整的加密工作流测试通过');
        print('   - 密钥协商: ✅');
        print('   - 数据加密: ✅');
        print('   - 数据解密: ✅');
        print('   - 校验和验证: ✅');
        print('   - 序列号处理: ✅');
      } finally {
        securityManager.dispose();
      }
    });

    test('Encryption should be disabled when user disables it', () async {
      final securityManager = BluFiSecurityManager();

      try {
        await securityManager.initialize();

        // 禁用加密
        securityManager.setEncryptionEnabled(false);

        // 即使完成协商，加密也应该被禁用
        final negotiationData = await securityManager.startKeyNegotiation();
        expect(negotiationData, isNotNull);

        final mockPeerPublicKey = Uint8List(128);
        for (int i = 0; i < 128; i++) {
          mockPeerPublicKey[i] = i % 256;
        }

        await securityManager.handleNegotiationData(mockPeerPublicKey);
        expect(securityManager.isNegotiationComplete, isTrue);
        expect(securityManager.isEncryptionEnabled, isFalse); // 应该是false

        // 数据应该不被加密
        final testData = Uint8List.fromList('test'.codeUnits);
        final result = securityManager.encryptData(testData, 1);
        expect(result, equals(testData)); // 应该返回原始数据

        print('✅ 用户禁用加密功能测试通过');
      } finally {
        securityManager.dispose();
      }
    });

    test('Error handling should work correctly', () async {
      final securityManager = BluFiSecurityManager();

      try {
        // 未初始化时的错误处理
        final negotiationData = await securityManager.startKeyNegotiation();
        expect(negotiationData, isNull);

        // 初始化后测试
        await securityManager.initialize();

        // 无效的协商数据
        final invalidData = Uint8List(10); // 太短
        final result = await securityManager.handleNegotiationData(invalidData);
        expect(result, isFalse);

        // 测试在没有协商完成时的行为
        final testData = Uint8List.fromList('test'.codeUnits);
        securityManager.setEncryptionEnabled(true);
        // 由于没有完成密钥协商，isEncryptionEnabled应该返回false
        expect(securityManager.isEncryptionEnabled, isFalse);
        // 因此encryptData应该返回原始数据（不加密）
        final encryptResult = securityManager.encryptData(testData, 1);
        expect(encryptResult, equals(testData)); // 应该返回原始数据

        print('✅ 错误处理测试通过');
      } finally {
        securityManager.dispose();
      }
    });
  });
}
