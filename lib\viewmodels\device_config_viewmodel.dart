import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../models/wifi_config.dart';
import '../models/connection_state.dart';
import '../services/blufi_service.dart';
import '../services/blufi_security_manager.dart';
import '../utils/blufi_constants.dart';

/// 设备配置视图模型
/// 负责管理WiFi配置相关的业务逻辑和状态
class DeviceConfigViewModel extends ChangeNotifier {
  static final Logger _logger = Logger();

  final BluFiService _blufiService;

  // 配置状态
  bool _isConfiguring = false;
  bool _isReady = false;
  bool _isLoadingWifiList = false;

  // WiFi配置数据
  int _selectedWifiMode = BluFiConstants.wifiModeSta;
  String _staSsid = '';
  String _staPassword = '';
  String _softApSsid = '';
  String _softApPassword = '';
  int _softApAuthMode = BluFiConstants.wifiAuthWpa2Psk;
  int _softApChannel = 1;
  int _softApMaxConnections = 4;

  // WiFi列表和状态
  List<String> _availableWifiNetworks = [];
  WiFiConnectionState? _wifiConnectionState;

  // 错误信息
  String? _errorMessage;

  // 订阅管理
  StreamSubscription? _stateSubscription;
  StreamSubscription? _wifiStateSubscription;
  StreamSubscription? _wifiListSubscription;
  StreamSubscription? _errorSubscription;

  // 状态检查定时器
  Timer? _stateCheckTimer;

  DeviceConfigViewModel(this._blufiService);

  // Getters - 状态
  bool get isConfiguring => _isConfiguring;
  bool get isReady => _isReady;
  bool get isLoadingWifiList => _isLoadingWifiList;
  String? get errorMessage => _errorMessage;
  BluFiDevice? get connectedDevice => _blufiService.connectedDevice;
  bool get hasConnectedDevice => connectedDevice != null;

  /// 获取安全管理器
  BluFiSecurityManager? getSecurityManager() {
    try {
      // 通过BluFiService的内部协议服务获取安全管理器
      // 需要在BluFiService中添加相应的getter方法
      return _blufiService.getSecurityManager();
    } catch (e) {
      _logger.e('Failed to get security manager: $e');
      return null;
    }
  }

  /// 获取BluFi服务实例
  BluFiService get blufiService => _blufiService;

  // Getters - WiFi配置
  int get selectedWifiMode => _selectedWifiMode;
  String get staSsid => _staSsid;
  String get staPassword => _staPassword;
  String get softApSsid => _softApSsid;
  String get softApPassword => _softApPassword;
  int get softApAuthMode => _softApAuthMode;
  int get softApChannel => _softApChannel;
  int get softApMaxConnections => _softApMaxConnections;

  // Getters - WiFi列表和状态
  List<String> get availableWifiNetworks =>
      List.unmodifiable(_availableWifiNetworks);
  WiFiConnectionState? get wifiConnectionState => _wifiConnectionState;
  bool get hasWifiNetworks => _availableWifiNetworks.isNotEmpty;

  /// 初始化视图模型
  Future<void> initialize() async {
    try {
      _logger.i('Initializing device config view model');

      _setupEventListeners();
      _checkServiceState();
      _startStateCheckTimer();

      _logger.i('Device config view model initialized');
    } catch (e) {
      _logger.e('Failed to initialize device config view model: $e');
      _setError('初始化失败: $e');
    }
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听服务状态
    _stateSubscription = _blufiService.stateStream.listen(
      (state) {
        final wasReady = _isReady;
        _isReady = state == BluFiServiceState.ready;
        _isConfiguring = state == BluFiServiceState.configuring;

        if (wasReady != _isReady || _isConfiguring) {
          _logger.d('Service state changed: $state');
          notifyListeners();
        }
      },
      onError: (error) {
        _logger.e('State stream error: $error');
        _setError('状态监听错误: $error');
      },
    );

    // 监听WiFi连接状态
    _wifiStateSubscription = _blufiService.wifiStateStream.listen(
      (state) {
        _wifiConnectionState = state;
        _logger.d('WiFi state updated: ${state.staStateDescription}');
        notifyListeners();
      },
      onError: (error) {
        _logger.e('WiFi state stream error: $error');
        _setError('WiFi状态监听错误: $error');
      },
    );

    // 监听WiFi列表
    _wifiListSubscription = _blufiService.wifiListStream.listen(
      (networks) {
        _availableWifiNetworks = networks;
        _isLoadingWifiList = false;
        _logger.d('WiFi networks updated: ${networks.length} networks');
        notifyListeners();
      },
      onError: (error) {
        _logger.e('WiFi list stream error: $error');
        _isLoadingWifiList = false;
        _setError('WiFi列表获取错误: $error');
      },
    );

    // 监听错误
    _errorSubscription = _blufiService.errorStream.listen(
      (error) {
        _logger.w('BluFi service error: $error');
        _setError(error);
      },
    );
  }

  /// 检查服务状态
  void _checkServiceState() {
    final currentState = _blufiService.state;
    _logger.d('Checking service state: $currentState');

    final wasReady = _isReady;
    final wasConfiguring = _isConfiguring;

    _isReady = currentState == BluFiServiceState.ready;
    _isConfiguring = currentState == BluFiServiceState.configuring;

    if (wasReady != _isReady || wasConfiguring != _isConfiguring) {
      _logger.i(
          'Service state updated: ready=$_isReady, configuring=$_isConfiguring');
      notifyListeners();
    }
  }

  /// 强制刷新状态
  void forceRefreshState() {
    _logger.i('Force refreshing state');
    _checkServiceState();

    // 如果服务状态显示已连接但UI还在显示连接中，强制更新
    if (_blufiService.state == BluFiServiceState.ready && !_isReady) {
      _logger.w('State mismatch detected, forcing update');
      _isReady = true;
      _isConfiguring = false;
      _clearError();
      notifyListeners();
    }
  }

  /// 启动状态检查定时器
  void _startStateCheckTimer() {
    _stateCheckTimer?.cancel();
    _stateCheckTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _checkServiceState();
    });
  }

  /// 停止状态检查定时器
  void _stopStateCheckTimer() {
    _stateCheckTimer?.cancel();
    _stateCheckTimer = null;
  }

  /// 设置WiFi模式
  void setWifiMode(int mode) {
    if (_selectedWifiMode != mode) {
      _selectedWifiMode = mode;
      _logger.d('WiFi mode changed to: $mode');
      notifyListeners();
    }
  }

  /// 设置Station配置
  void setStationConfig({String? ssid, String? password}) {
    bool changed = false;

    if (ssid != null && _staSsid != ssid) {
      _staSsid = ssid;
      changed = true;
    }

    if (password != null && _staPassword != password) {
      _staPassword = password;
      changed = true;
    }

    if (changed) {
      _logger.d('Station config updated');
      notifyListeners();
    }
  }

  /// 设置SoftAP配置
  void setSoftApConfig({
    String? ssid,
    String? password,
    int? authMode,
    int? channel,
    int? maxConnections,
  }) {
    bool changed = false;

    if (ssid != null && _softApSsid != ssid) {
      _softApSsid = ssid;
      changed = true;
    }

    if (password != null && _softApPassword != password) {
      _softApPassword = password;
      changed = true;
    }

    if (authMode != null && _softApAuthMode != authMode) {
      _softApAuthMode = authMode;
      changed = true;
    }

    if (channel != null && _softApChannel != channel) {
      _softApChannel = channel;
      changed = true;
    }

    if (maxConnections != null && _softApMaxConnections != maxConnections) {
      _softApMaxConnections = maxConnections;
      changed = true;
    }

    if (changed) {
      _logger.d('SoftAP config updated');
      notifyListeners();
    }
  }

  /// 获取WiFi列表
  Future<void> getWifiList() async {
    if (!_isReady || _isLoadingWifiList) {
      _logger.w(
          'Cannot get WiFi list: isReady=$_isReady, isLoading=$_isLoadingWifiList');
      return;
    }

    try {
      _logger.i('Getting WiFi list');
      _isLoadingWifiList = true;
      _clearError();
      notifyListeners();

      await _blufiService.getWiFiList();
    } catch (e) {
      _logger.e('Failed to get WiFi list: $e');
      _isLoadingWifiList = false;
      _setError('获取WiFi列表失败: $e');
    }
  }

  /// 配置WiFi
  Future<bool> configureWifi() async {
    if (!_isReady || _isConfiguring) {
      _logger.w(
          'Cannot configure WiFi: isReady=$_isReady, isConfiguring=$_isConfiguring');
      return false;
    }

    if (!_validateConfiguration()) {
      return false;
    }

    try {
      _logger.i('Configuring WiFi with mode: $_selectedWifiMode');
      _clearError();

      final config = _buildWifiConfig();
      final success = await _blufiService.configureWiFi(config);

      if (success) {
        _logger.i('WiFi configuration sent successfully');
        // 配置发送成功后，获取WiFi状态
        Timer(const Duration(seconds: 2), () {
          getWifiStatus();
        });
      } else {
        _setError('WiFi配置失败');
      }

      return success;
    } catch (e) {
      _logger.e('Failed to configure WiFi: $e');
      _setError('WiFi配置失败: $e');
      return false;
    }
  }

  /// 获取WiFi状态
  Future<void> getWifiStatus() async {
    if (!_isReady) {
      _logger.w('Cannot get WiFi status: service not ready');
      return;
    }

    try {
      _logger.i('Getting WiFi status');
      await _blufiService.getWiFiStatus();
    } catch (e) {
      _logger.e('Failed to get WiFi status: $e');
      _setError('获取WiFi状态失败: $e');
    }
  }

  /// 验证配置
  bool _validateConfiguration() {
    switch (_selectedWifiMode) {
      case BluFiConstants.wifiModeSta:
        if (_staSsid.isEmpty) {
          _setError('请输入WiFi名称');
          return false;
        }
        if (_staPassword.isEmpty) {
          _setError('请输入WiFi密码');
          return false;
        }
        break;

      case BluFiConstants.wifiModeAp:
        if (_softApSsid.isEmpty) {
          _setError('请输入热点名称');
          return false;
        }
        if (_softApPassword.isEmpty) {
          _setError('请输入热点密码');
          return false;
        }
        break;

      case BluFiConstants.wifiModeApSta:
        if (_staSsid.isEmpty || _staPassword.isEmpty) {
          _setError('请完整填写Station模式配置');
          return false;
        }
        if (_softApSsid.isEmpty || _softApPassword.isEmpty) {
          _setError('请完整填写SoftAP模式配置');
          return false;
        }
        break;

      default:
        _setError('请选择WiFi模式');
        return false;
    }

    return true;
  }

  /// 构建WiFi配置
  WiFiConfig _buildWifiConfig() {
    switch (_selectedWifiMode) {
      case BluFiConstants.wifiModeSta:
        return WiFiConfig.station(
          ssid: _staSsid,
          password: _staPassword,
        );

      case BluFiConstants.wifiModeAp:
        return WiFiConfig.softAp(
          ssid: _softApSsid,
          password: _softApPassword,
          authMode: _softApAuthMode,
          channel: _softApChannel,
          maxConnections: _softApMaxConnections,
        );

      case BluFiConstants.wifiModeApSta:
        return WiFiConfig.stationSoftAp(
          stationConfig: StationConfig(
            ssid: _staSsid,
            password: _staPassword,
          ),
          softApConfig: SoftApConfig(
            ssid: _softApSsid,
            password: _softApPassword,
            authMode: _softApAuthMode,
            channel: _softApChannel,
            maxConnections: _softApMaxConnections,
          ),
        );

      default:
        throw ArgumentError('Invalid WiFi mode: $_selectedWifiMode');
    }
  }

  /// 获取WiFi模式描述
  String get wifiModeDescription {
    switch (_selectedWifiMode) {
      case BluFiConstants.wifiModeSta:
        return 'Station模式';
      case BluFiConstants.wifiModeAp:
        return 'SoftAP模式';
      case BluFiConstants.wifiModeApSta:
        return 'Station+SoftAP模式';
      default:
        return '未知模式';
    }
  }

  /// 获取认证模式描述
  String get authModeDescription {
    switch (_softApAuthMode) {
      case BluFiConstants.wifiAuthOpen:
        return '开放';
      case BluFiConstants.wifiAuthWep:
        return 'WEP';
      case BluFiConstants.wifiAuthWpaPsk:
        return 'WPA PSK';
      case BluFiConstants.wifiAuthWpa2Psk:
        return 'WPA2 PSK';
      case BluFiConstants.wifiAuthWpaWpa2Psk:
        return 'WPA/WPA2 PSK';
      default:
        return '未知';
    }
  }

  /// 检查是否可以配置
  bool get canConfigure => _isReady && !_isConfiguring;

  /// 重置配置
  void resetConfiguration() {
    _logger.i('Resetting WiFi configuration');

    _selectedWifiMode = BluFiConstants.wifiModeSta;
    _staSsid = '';
    _staPassword = '';
    _softApSsid = '';
    _softApPassword = '';
    _softApAuthMode = BluFiConstants.wifiAuthWpa2Psk;
    _softApChannel = 1;
    _softApMaxConnections = 4;

    _clearError();
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _logger.i('Disposing device config view model');

    _stateSubscription?.cancel();
    _wifiStateSubscription?.cancel();
    _wifiListSubscription?.cancel();
    _errorSubscription?.cancel();
    _stopStateCheckTimer();

    super.dispose();
  }
}
