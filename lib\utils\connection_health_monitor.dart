import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import 'connection_cache.dart';

/// 连接健康监控器
/// 实时监控蓝牙连接健康状态并提供优化建议
class ConnectionHealthMonitor {
  static final Logger _logger = Logger();
  static ConnectionHealthMonitor? _instance;
  
  final Map<String, DeviceHealthStatus> _deviceHealth = {};
  final Map<String, StreamSubscription> _healthSubscriptions = {};
  Timer? _healthCheckTimer;
  
  static ConnectionHealthMonitor get instance {
    _instance ??= ConnectionHealthMonitor._();
    return _instance!;
  }
  
  ConnectionHealthMonitor._();
  
  /// 开始监控设备健康状态
  void startMonitoring(BluFiDevice device) {
    final deviceId = device.bluetoothDevice.remoteId.toString();
    
    if (_healthSubscriptions.containsKey(deviceId)) {
      _logger.w('Already monitoring device: $deviceId');
      return;
    }
    
    _logger.i('Starting health monitoring for device: ${device.name}');
    
    // 初始化健康状态
    _deviceHealth[deviceId] = DeviceHealthStatus(
      deviceId: deviceId,
      deviceName: device.name,
      startTime: DateTime.now(),
    );
    
    // 监听连接状态变化
    final subscription = device.bluetoothDevice.connectionState.listen(
      (state) => _onConnectionStateChanged(deviceId, state),
      onError: (error) => _onConnectionError(deviceId, error),
    );
    
    _healthSubscriptions[deviceId] = subscription;
    
    // 启动定期健康检查
    _startPeriodicHealthCheck();
  }
  
  /// 停止监控设备
  void stopMonitoring(String deviceId) {
    _logger.i('Stopping health monitoring for device: $deviceId');
    
    _healthSubscriptions[deviceId]?.cancel();
    _healthSubscriptions.remove(deviceId);
    _deviceHealth.remove(deviceId);
    
    if (_healthSubscriptions.isEmpty) {
      _stopPeriodicHealthCheck();
    }
  }
  
  /// 获取设备健康状态
  DeviceHealthStatus? getDeviceHealth(String deviceId) {
    return _deviceHealth[deviceId];
  }
  
  /// 获取所有设备健康状态
  Map<String, DeviceHealthStatus> getAllDeviceHealth() {
    return Map.unmodifiable(_deviceHealth);
  }
  
  /// 连接状态变化处理
  void _onConnectionStateChanged(String deviceId, fbp.BluetoothConnectionState state) {
    final health = _deviceHealth[deviceId];
    if (health == null) return;
    
    health.lastStateChange = DateTime.now();
    health.currentState = state;
    
    switch (state) {
      case fbp.BluetoothConnectionState.connected:
        health.connectionCount++;
        health.lastConnectedTime = DateTime.now();
        health.isCurrentlyConnected = true;
        _logger.d('Device $deviceId connected (count: ${health.connectionCount})');
        break;
        
      case fbp.BluetoothConnectionState.disconnected:
        if (health.isCurrentlyConnected) {
          health.disconnectionCount++;
          health.lastDisconnectedTime = DateTime.now();
          health.isCurrentlyConnected = false;
          
          // 计算连接持续时间
          if (health.lastConnectedTime != null) {
            final duration = DateTime.now().difference(health.lastConnectedTime!);
            health.connectionDurations.add(duration);
            
            // 只保留最近20次记录
            if (health.connectionDurations.length > 20) {
              health.connectionDurations.removeAt(0);
            }
          }
          
          _logger.d('Device $deviceId disconnected (count: ${health.disconnectionCount})');
        }
        break;
        
      default:
        break;
    }
    
    // 更新健康评分
    _updateHealthScore(health);
  }
  
  /// 连接错误处理
  void _onConnectionError(String deviceId, dynamic error) {
    final health = _deviceHealth[deviceId];
    if (health == null) return;
    
    health.errorCount++;
    health.lastError = error.toString();
    health.lastErrorTime = DateTime.now();
    
    _logger.w('Connection error for device $deviceId: $error');
    
    // 更新健康评分
    _updateHealthScore(health);
  }
  
  /// 更新健康评分
  void _updateHealthScore(DeviceHealthStatus health) {
    double score = 100.0;
    
    // 基于连接稳定性评分
    if (health.connectionCount > 0) {
      final disconnectionRate = health.disconnectionCount / health.connectionCount;
      score -= disconnectionRate * 30; // 断开率影响30分
    }
    
    // 基于错误率评分
    final totalEvents = health.connectionCount + health.disconnectionCount + health.errorCount;
    if (totalEvents > 0) {
      final errorRate = health.errorCount / totalEvents;
      score -= errorRate * 40; // 错误率影响40分
    }
    
    // 基于连接持续时间评分
    if (health.averageConnectionDuration != null) {
      final avgMinutes = health.averageConnectionDuration!.inMinutes;
      if (avgMinutes < 1) {
        score -= 20; // 连接时间太短扣20分
      } else if (avgMinutes > 10) {
        score += 10; // 长时间连接加10分
      }
    }
    
    // 基于最近错误时间评分
    if (health.lastErrorTime != null) {
      final timeSinceError = DateTime.now().difference(health.lastErrorTime!);
      if (timeSinceError.inMinutes < 5) {
        score -= 15; // 最近有错误扣15分
      }
    }
    
    health.healthScore = score.clamp(0, 100);
  }
  
  /// 启动定期健康检查
  void _startPeriodicHealthCheck() {
    if (_healthCheckTimer != null) return;
    
    _healthCheckTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _performHealthCheck();
    });
  }
  
  /// 停止定期健康检查
  void _stopPeriodicHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }
  
  /// 执行健康检查
  void _performHealthCheck() {
    for (final health in _deviceHealth.values) {
      _updateHealthScore(health);
      
      // 检查是否需要发出警告
      if (health.healthScore < 50) {
        _logger.w('Device ${health.deviceName} health score low: ${health.healthScore.toStringAsFixed(1)}');
      }
      
      // 检查连接是否异常断开
      if (health.isCurrentlyConnected && health.lastStateChange != null) {
        final timeSinceLastChange = DateTime.now().difference(health.lastStateChange!);
        if (timeSinceLastChange.inMinutes > 30) {
          _logger.i('Device ${health.deviceName} has been connected for ${timeSinceLastChange.inMinutes} minutes');
        }
      }
    }
  }
  
  /// 生成健康报告
  String generateHealthReport() {
    if (_deviceHealth.isEmpty) {
      return 'No devices being monitored';
    }
    
    final buffer = StringBuffer();
    buffer.writeln('=== Connection Health Report ===');
    buffer.writeln('Monitoring ${_deviceHealth.length} device(s)');
    buffer.writeln();
    
    for (final health in _deviceHealth.values) {
      buffer.writeln('Device: ${health.deviceName}');
      buffer.writeln('  Health Score: ${health.healthScore.toStringAsFixed(1)}/100');
      buffer.writeln('  Status: ${health.isCurrentlyConnected ? 'Connected' : 'Disconnected'}');
      buffer.writeln('  Connections: ${health.connectionCount}');
      buffer.writeln('  Disconnections: ${health.disconnectionCount}');
      buffer.writeln('  Errors: ${health.errorCount}');
      
      if (health.averageConnectionDuration != null) {
        buffer.writeln('  Avg Connection Time: ${health.averageConnectionDuration!.inMinutes}m');
      }
      
      if (health.lastError != null) {
        buffer.writeln('  Last Error: ${health.lastError}');
      }
      
      buffer.writeln();
    }
    
    buffer.writeln('================================');
    return buffer.toString();
  }
  
  /// 清理资源
  void dispose() {
    for (final subscription in _healthSubscriptions.values) {
      subscription.cancel();
    }
    _healthSubscriptions.clear();
    _deviceHealth.clear();
    _stopPeriodicHealthCheck();
  }
}

/// 设备健康状态
class DeviceHealthStatus {
  final String deviceId;
  final String deviceName;
  final DateTime startTime;
  
  fbp.BluetoothConnectionState? currentState;
  DateTime? lastStateChange;
  DateTime? lastConnectedTime;
  DateTime? lastDisconnectedTime;
  DateTime? lastErrorTime;
  String? lastError;
  
  bool isCurrentlyConnected = false;
  int connectionCount = 0;
  int disconnectionCount = 0;
  int errorCount = 0;
  double healthScore = 100.0;
  
  final List<Duration> connectionDurations = [];
  
  DeviceHealthStatus({
    required this.deviceId,
    required this.deviceName,
    required this.startTime,
  });
  
  /// 平均连接持续时间
  Duration? get averageConnectionDuration {
    if (connectionDurations.isEmpty) return null;
    
    final totalMs = connectionDurations
        .map((d) => d.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: (totalMs / connectionDurations.length).round());
  }
  
  /// 连接稳定性评级
  String get stabilityRating {
    if (healthScore >= 90) return 'Excellent';
    if (healthScore >= 75) return 'Good';
    if (healthScore >= 60) return 'Fair';
    if (healthScore >= 40) return 'Poor';
    return 'Critical';
  }
  
  /// 监控持续时间
  Duration get monitoringDuration => DateTime.now().difference(startTime);
  
  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'healthScore': healthScore,
      'stabilityRating': stabilityRating,
      'isCurrentlyConnected': isCurrentlyConnected,
      'connectionCount': connectionCount,
      'disconnectionCount': disconnectionCount,
      'errorCount': errorCount,
      'averageConnectionDurationMs': averageConnectionDuration?.inMilliseconds,
      'monitoringDurationMs': monitoringDuration.inMilliseconds,
      'lastError': lastError,
    };
  }
}
