import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/connection_viewmodel.dart';
import '../widgets/status_indicator.dart';
import '../widgets/error_display.dart';
import '../utils/constants.dart';
import 'device_scan_page.dart';

/// 连接状态页面
/// 显示设备连接状态和WiFi信息
class ConnectionStatusPage extends StatefulWidget {
  const ConnectionStatusPage({Key? key}) : super(key: key);

  @override
  State<ConnectionStatusPage> createState() => _ConnectionStatusPageState();
}

class _ConnectionStatusPageState extends State<ConnectionStatusPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ConnectionViewModel>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('连接状态'),
        actions: [
          Consumer<ConnectionViewModel>(
            builder: (context, viewModel, child) {
              return PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(viewModel, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'query_version',
                    child: ListTile(
                      leading: Icon(Icons.info_outline),
                      title: Text('查询版本'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'refresh_wifi',
                    child: ListTile(
                      leading: Icon(Icons.wifi_find),
                      title: Text('刷新WiFi状态'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'reconnect',
                    child: ListTile(
                      leading: Icon(Icons.refresh),
                      title: Text('重新连接'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'disconnect',
                    child: ListTile(
                      leading: Icon(Icons.bluetooth_disabled),
                      title: Text('断开连接'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'scan_new',
                    child: ListTile(
                      leading: Icon(Icons.search),
                      title: Text('扫描新设备'),
                      dense: true,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<ConnectionViewModel>(
        builder: (context, viewModel, child) {
          return RefreshIndicator(
            onRefresh: () => _refreshStatus(viewModel),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildConnectionOverview(viewModel),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildDeviceInfo(viewModel),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildWifiInfo(viewModel),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildConnectionQuality(viewModel),
                  if (viewModel.errorMessage != null) ...[
                    const SizedBox(height: AppConstants.largePadding),
                    _buildErrorInfo(viewModel),
                  ],
                ],
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: Consumer<ConnectionViewModel>(
        builder: (context, viewModel, child) {
          return _buildBottomActions(viewModel);
        },
      ),
    );
  }

  /// 构建连接概览
  Widget _buildConnectionOverview(ConnectionViewModel viewModel) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          children: [
            Icon(
              _getConnectionIcon(viewModel),
              size: 64,
              color: _getConnectionColor(viewModel),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              viewModel.connectionStatusDescription,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getConnectionColor(viewModel),
                  ),
            ),
            if (viewModel.connectionDuration != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                '连接时长: ${viewModel.connectionDurationDescription}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建设备信息
  Widget _buildDeviceInfo(ConnectionViewModel viewModel) {
    final deviceInfo = Map<String, String>.from(viewModel.deviceInfo);

    // 添加版本信息
    if (viewModel.deviceVersion != null) {
      deviceInfo['版本'] = viewModel.deviceVersion!;
    } else if (viewModel.isConnected) {
      deviceInfo['版本'] = viewModel.isQueryingVersion ? '查询中...' : '未知';
    }

    if (deviceInfo.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bluetooth, color: Colors.blue),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    '设备信息',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                  ),
                ),
                if (viewModel.isConnected)
                  IconButton(
                    onPressed: viewModel.isQueryingVersion
                        ? null
                        : () => _queryVersion(viewModel),
                    icon: viewModel.isQueryingVersion
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.info_outline),
                    tooltip: '查询版本',
                  ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...deviceInfo.entries.map((entry) => Padding(
                  padding:
                      const EdgeInsets.only(bottom: AppConstants.smallPadding),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 80,
                        child: Text(
                          '${entry.key}:',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: Colors.grey[700],
                                  ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          entry.value,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  /// 构建WiFi信息
  Widget _buildWifiInfo(ConnectionViewModel viewModel) {
    final wifiInfo = viewModel.wifiInfo;

    return InfoCard(
      title: 'WiFi信息',
      icon: Icons.wifi,
      color: viewModel.isWifiConnected ? Colors.green : Colors.orange,
      info: wifiInfo.isNotEmpty ? wifiInfo : {'状态': '未配置'},
    );
  }

  /// 构建连接质量
  Widget _buildConnectionQuality(ConnectionViewModel viewModel) {
    if (!viewModel.isConnected) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.signal_cellular_alt, color: Colors.blue),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  '连接质量',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ConnectionQualityIndicator(
              quality: viewModel.connectionQuality,
              label: '信号强度',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误信息
  Widget _buildErrorInfo(ConnectionViewModel viewModel) {
    return ErrorDisplay(
      errorMessage: viewModel.errorMessage!,
      onRetry: () => _retryLastOperation(viewModel),
      onDismiss: () => viewModel.clearError(),
      showSuggestions: true,
    );
  }

  /// 重试上次操作
  void _retryLastOperation(ConnectionViewModel viewModel) {
    // 根据当前状态决定重试操作
    if (!viewModel.isConnected) {
      _reconnect(viewModel);
    } else {
      // 可以添加更多智能重试逻辑
      _refreshStatus(viewModel);
    }
  }

  /// 构建底部操作
  Widget _buildBottomActions(ConnectionViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            if (viewModel.canDisconnect) ...[
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _disconnect(viewModel),
                  icon: const Icon(Icons.bluetooth_disabled),
                  label: const Text('断开连接'),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
            ],
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _navigateToScan(),
                icon: const Icon(Icons.search),
                label: const Text('扫描设备'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取连接图标
  IconData _getConnectionIcon(ConnectionViewModel viewModel) {
    if (viewModel.isConnected) {
      return Icons.bluetooth_connected;
    } else if (viewModel.isConnecting) {
      return Icons.bluetooth_searching;
    } else if (viewModel.hasError) {
      return Icons.bluetooth_disabled;
    } else {
      return Icons.bluetooth;
    }
  }

  /// 获取连接颜色
  Color _getConnectionColor(ConnectionViewModel viewModel) {
    if (viewModel.isConnected) {
      return Colors.green;
    } else if (viewModel.isConnecting) {
      return Colors.orange;
    } else if (viewModel.hasError) {
      return Colors.red;
    } else {
      return Colors.grey;
    }
  }

  /// 处理菜单操作
  void _handleMenuAction(ConnectionViewModel viewModel, String action) {
    switch (action) {
      case 'query_version':
        _queryVersion(viewModel);
        break;
      case 'refresh_wifi':
        _refreshWifiStatus(viewModel);
        break;
      case 'reconnect':
        _reconnect(viewModel);
        break;
      case 'disconnect':
        _disconnect(viewModel);
        break;
      case 'scan_new':
        _navigateToScan();
        break;
    }
  }

  /// 重新连接
  Future<void> _reconnect(ConnectionViewModel viewModel) async {
    try {
      _showMessage('正在重新连接...');
      final success = await viewModel.reconnect();

      if (success) {
        _showMessage('重新连接成功');
      } else {
        _showMessage('重新连接失败');
      }
    } catch (e) {
      _showMessage('重新连接错误: $e');
    }
  }

  /// 断开连接
  Future<void> _disconnect(ConnectionViewModel viewModel) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('断开连接'),
        content: const Text('确定要断开与设备的连接吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await viewModel.disconnect();
        _showMessage('已断开连接');
      } catch (e) {
        _showMessage('断开连接失败: $e');
      }
    }
  }

  /// 导航到扫描页面
  void _navigateToScan() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const DeviceScanPageProvider(),
      ),
      (route) => false,
    );
  }

  /// 查询设备版本
  Future<void> _queryVersion(ConnectionViewModel viewModel) async {
    try {
      _showMessage('正在查询设备版本...');
      final success = await viewModel.queryDeviceVersion();

      if (success) {
        _showMessage('版本查询成功');
      } else {
        _showMessage('版本查询失败');
      }
    } catch (e) {
      _showMessage('查询版本错误: $e');
    }
  }

  /// 刷新WiFi状态
  Future<void> _refreshWifiStatus(ConnectionViewModel viewModel) async {
    try {
      _showMessage('正在刷新WiFi状态...');
      final success = await viewModel.refreshWifiStatus();

      if (success) {
        _showMessage('WiFi状态刷新成功');
      } else {
        _showMessage('WiFi状态刷新失败');
      }
    } catch (e) {
      _showMessage('刷新WiFi状态错误: $e');
    }
  }

  /// 刷新状态
  Future<void> _refreshStatus(ConnectionViewModel viewModel) async {
    // 同时刷新版本和WiFi状态
    await Future.wait([
      _queryVersion(viewModel),
      _refreshWifiStatus(viewModel),
    ]);
  }

  /// 显示消息
  void _showMessage(String message) {
    if (!mounted) return;

    // 检查是否是错误消息
    if (message.contains('错误') || message.contains('失败')) {
      ErrorToast.show(context, message);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}

/// 连接状态页面的Provider包装器
class ConnectionStatusPageProvider extends StatelessWidget {
  const ConnectionStatusPageProvider({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ConnectionViewModel(
        context.read(), // BluFiService
      ),
      child: const ConnectionStatusPage(),
    );
  }
}
