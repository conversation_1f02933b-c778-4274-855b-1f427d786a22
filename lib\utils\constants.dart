/// 应用通用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'ESP32 BluFi';
  static const String appVersion = '1.0.0';

  // 蓝牙相关
  static const int scanTimeoutSeconds = 10;
  static const int connectionTimeoutSeconds = 5; // 增加到5秒以提高成功率
  static const int fastConnectionTimeoutSeconds = 3; // 增加到3秒
  static const int connectionRetryDelayMs = 1000; // 增加重试延迟到1秒
  static const int maxConnectionRetries = 3; // 增加重试次数到3次
  static const int mtuRequestSize = 247; // 优化的MTU大小
  static const int connectionStateTimeoutSeconds = 3; // 增加状态确认超时
  static const int minSignalStrength = -85; // 最小信号强度要求
  static const int maxConcurrentConnections = 2; // 最大并发连接数
  static const String blufiDeviceNamePrefix = 'BLUFI_DEVICE';

  // UI相关
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;

  // 动画时长
  static const int animationDurationMs = 300;
  static const int longAnimationDurationMs = 500;

  // 网络配置
  static const int wifiScanTimeoutSeconds = 30;
  static const int configTimeoutSeconds = 60;

  // 本地存储键
  static const String keyLastConnectedDevice = 'last_connected_device';
  static const String keyWifiHistory = 'wifi_history';
  static const String keyAppSettings = 'app_settings';
}

/// 应用主题颜色
class AppColors {
  // 主色调
  static const int primaryColorValue = 0xFF2196F3;
  static const int accentColorValue = 0xFF03DAC6;

  // 状态颜色
  static const int successColorValue = 0xFF4CAF50;
  static const int warningColorValue = 0xFFFF9800;
  static const int errorColorValue = 0xFFF44336;
  static const int infoColorValue = 0xFF2196F3;

  // 文本颜色
  static const int primaryTextColorValue = 0xFF212121;
  static const int secondaryTextColorValue = 0xFF757575;
  static const int hintTextColorValue = 0xFF9E9E9E;

  // 背景颜色
  static const int backgroundColorValue = 0xFFFAFAFA;
  static const int surfaceColorValue = 0xFFFFFFFF;
  static const int dividerColorValue = 0xFFE0E0E0;
}

/// 应用字符串资源
class AppStrings {
  // 通用
  static const String ok = '确定';
  static const String cancel = '取消';
  static const String retry = '重试';
  static const String loading = '加载中...';
  static const String error = '错误';
  static const String success = '成功';
  static const String warning = '警告';
  static const String info = '信息';

  // 蓝牙相关
  static const String bluetoothDisabled = '蓝牙未开启';
  static const String bluetoothPermissionDenied = '蓝牙权限被拒绝';
  static const String scanningDevices = '正在扫描设备...';
  static const String noDevicesFound = '未发现设备';
  static const String connectingToDevice = '正在连接设备...';
  static const String deviceConnected = '设备已连接';
  static const String deviceDisconnected = '设备已断开';
  static const String connectionFailed = '连接失败';

  // WiFi配置相关
  static const String wifiConfiguration = 'WiFi配置';
  static const String selectWifiMode = '选择WiFi模式';
  static const String stationMode = 'Station模式';
  static const String softApMode = 'SoftAP模式';
  static const String stationSoftApMode = 'Station+SoftAP模式';
  static const String wifiSsid = 'WiFi名称(SSID)';
  static const String wifiPassword = 'WiFi密码';
  static const String wifiSecurity = '安全模式';
  static const String wifiChannel = '信道';
  static const String maxConnections = '最大连接数';
  static const String configuring = '正在配置...';
  static const String configurationComplete = '配置完成';
  static const String configurationFailed = '配置失败';

  // 状态相关
  static const String connectionStatus = '连接状态';
  static const String wifiStatus = 'WiFi状态';
  static const String ipAddress = 'IP地址';
  static const String macAddress = 'MAC地址';
  static const String signalStrength = '信号强度';

  // 错误消息
  static const String unknownError = '未知错误';
  static const String networkError = '网络错误';
  static const String timeoutError = '操作超时';
  static const String protocolError = '协议错误';
  static const String encryptionError = '加密错误';
  static const String authenticationError = '认证错误';
}
