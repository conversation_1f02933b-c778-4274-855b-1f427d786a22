import 'dart:async';
import 'dart:io';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import 'connection_cache.dart';

/// 连接失败分析器
/// 分析连接失败的原因并提供修复建议
class ConnectionFailureAnalyzer {
  static final Logger _logger = Logger();
  
  /// 分析连接失败的原因
  static Future<ConnectionFailureAnalysis> analyzeFailure(
    BluFiDevice device,
    Exception error,
    Duration attemptDuration,
  ) async {
    final deviceId = device.bluetoothDevice.remoteId.toString();
    final analysis = ConnectionFailureAnalysis(
      deviceId: deviceId,
      deviceName: device.name,
      error: error,
      attemptDuration: attemptDuration,
      timestamp: DateTime.now(),
    );
    
    // 分析错误类型
    analysis.failureType = _categorizeError(error);
    
    // 检查设备状态
    analysis.deviceState = await _checkDeviceState(device);
    
    // 检查蓝牙适配器状态
    analysis.adapterState = await _checkAdapterState();
    
    // 检查历史性能
    analysis.historicalPerformance = ConnectionStateCache.getPerformanceStats(deviceId);
    
    // 生成修复建议
    analysis.recommendations = _generateRecommendations(analysis);
    
    _logger.w('Connection failure analysis: ${analysis.summary}');
    
    return analysis;
  }
  
  /// 分类错误类型
  static ConnectionFailureType _categorizeError(Exception error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('timeout')) {
      return ConnectionFailureType.timeout;
    } else if (errorString.contains('permission')) {
      return ConnectionFailureType.permission;
    } else if (errorString.contains('bluetooth') && errorString.contains('off')) {
      return ConnectionFailureType.bluetoothOff;
    } else if (errorString.contains('device not found') || errorString.contains('not available')) {
      return ConnectionFailureType.deviceNotFound;
    } else if (errorString.contains('gatt') || errorString.contains('133')) {
      return ConnectionFailureType.gattError;
    } else if (errorString.contains('already connected')) {
      return ConnectionFailureType.alreadyConnected;
    } else if (errorString.contains('connection refused') || errorString.contains('rejected')) {
      return ConnectionFailureType.connectionRefused;
    } else {
      return ConnectionFailureType.unknown;
    }
  }
  
  /// 检查设备状态
  static Future<DeviceConnectionState> _checkDeviceState(BluFiDevice device) async {
    try {
      final connectionState = await device.bluetoothDevice.connectionState.first
          .timeout(const Duration(seconds: 2));
      
      final isConnected = connectionState == fbp.BluetoothConnectionState.connected;
      final rssi = device.rssi;
      
      return DeviceConnectionState(
        isConnected: isConnected,
        connectionState: connectionState,
        signalStrength: rssi,
        isInRange: rssi > -80, // 假设-80dBm为可接受范围
      );
    } catch (e) {
      return DeviceConnectionState(
        isConnected: false,
        connectionState: fbp.BluetoothConnectionState.disconnected,
        signalStrength: -100,
        isInRange: false,
      );
    }
  }
  
  /// 检查蓝牙适配器状态
  static Future<fbp.BluetoothAdapterState> _checkAdapterState() async {
    try {
      return await fbp.FlutterBluePlus.adapterState.first
          .timeout(const Duration(seconds: 2));
    } catch (e) {
      return fbp.BluetoothAdapterState.unknown;
    }
  }
  
  /// 生成修复建议
  static List<String> _generateRecommendations(ConnectionFailureAnalysis analysis) {
    final recommendations = <String>[];
    
    switch (analysis.failureType) {
      case ConnectionFailureType.timeout:
        recommendations.addAll([
          '增加连接超时时间',
          '检查设备是否在蓝牙范围内',
          '尝试重启设备蓝牙',
          '减少周围的无线干扰',
        ]);
        break;
        
      case ConnectionFailureType.permission:
        recommendations.addAll([
          '检查蓝牙权限设置',
          '重新授权应用蓝牙权限',
          '检查位置权限（Android 12+需要）',
        ]);
        break;
        
      case ConnectionFailureType.bluetoothOff:
        recommendations.addAll([
          '开启设备蓝牙',
          '等待蓝牙适配器就绪',
        ]);
        break;
        
      case ConnectionFailureType.deviceNotFound:
        recommendations.addAll([
          '确保设备处于可发现模式',
          '重新扫描设备',
          '检查设备是否已被其他应用连接',
          '重启目标设备',
        ]);
        break;
        
      case ConnectionFailureType.gattError:
        recommendations.addAll([
          '清理蓝牙缓存',
          '重启应用',
          '等待更长时间后重试',
          '检查设备固件版本',
        ]);
        break;
        
      case ConnectionFailureType.alreadyConnected:
        recommendations.addAll([
          '先断开现有连接',
          '检查连接状态同步',
        ]);
        break;
        
      case ConnectionFailureType.connectionRefused:
        recommendations.addAll([
          '检查设备是否支持连接',
          '验证设备配对状态',
          '重启目标设备',
        ]);
        break;
        
      case ConnectionFailureType.unknown:
        recommendations.addAll([
          '重试连接',
          '重启蓝牙适配器',
          '检查设备兼容性',
        ]);
        break;
    }
    
    // 基于信号强度的建议
    if (analysis.deviceState?.signalStrength != null) {
      final rssi = analysis.deviceState!.signalStrength!;
      if (rssi < -80) {
        recommendations.add('设备信号较弱($rssi dBm)，请靠近设备');
      }
    }
    
    // 基于历史性能的建议
    if (analysis.historicalPerformance != null) {
      final performance = analysis.historicalPerformance!;
      if (performance.successRate < 0.5) {
        recommendations.add('该设备历史连接成功率较低(${(performance.successRate * 100).toStringAsFixed(1)}%)，考虑重置设备缓存');
      }
    }
    
    return recommendations;
  }
  
  /// 自动修复尝试
  static Future<bool> attemptAutoFix(
    ConnectionFailureAnalysis analysis,
    BluFiDevice device,
  ) async {
    _logger.i('Attempting auto-fix for ${analysis.failureType}');
    
    try {
      switch (analysis.failureType) {
        case ConnectionFailureType.alreadyConnected:
          // 强制断开连接
          await device.bluetoothDevice.disconnect();
          await Future.delayed(const Duration(seconds: 1));
          return true;
          
        case ConnectionFailureType.gattError:
          // 清理连接状态
          await device.bluetoothDevice.disconnect();
          await Future.delayed(const Duration(seconds: 2));
          return true;
          
        case ConnectionFailureType.bluetoothOff:
          // 等待蓝牙开启
          if (Platform.isAndroid) {
            try {
              await fbp.FlutterBluePlus.turnOn();
              return true;
            } catch (e) {
              _logger.w('Cannot turn on Bluetooth automatically: $e');
            }
          }
          break;
          
        default:
          // 通用修复：等待一段时间
          await Future.delayed(const Duration(seconds: 1));
          return true;
      }
    } catch (e) {
      _logger.e('Auto-fix failed: $e');
    }
    
    return false;
  }
}

/// 连接失败类型
enum ConnectionFailureType {
  timeout,
  permission,
  bluetoothOff,
  deviceNotFound,
  gattError,
  alreadyConnected,
  connectionRefused,
  unknown,
}

/// 设备连接状态
class DeviceConnectionState {
  final bool isConnected;
  final fbp.BluetoothConnectionState connectionState;
  final int? signalStrength;
  final bool isInRange;
  
  DeviceConnectionState({
    required this.isConnected,
    required this.connectionState,
    this.signalStrength,
    required this.isInRange,
  });
}

/// 连接失败分析结果
class ConnectionFailureAnalysis {
  final String deviceId;
  final String deviceName;
  final Exception error;
  final Duration attemptDuration;
  final DateTime timestamp;
  
  late ConnectionFailureType failureType;
  DeviceConnectionState? deviceState;
  fbp.BluetoothAdapterState? adapterState;
  ConnectionPerformance? historicalPerformance;
  List<String> recommendations = [];
  
  ConnectionFailureAnalysis({
    required this.deviceId,
    required this.deviceName,
    required this.error,
    required this.attemptDuration,
    required this.timestamp,
  });
  
  String get summary {
    return 'Device: $deviceName, Type: $failureType, Duration: ${attemptDuration.inMilliseconds}ms, Error: ${error.toString()}';
  }
  
  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'failureType': failureType.toString(),
      'error': error.toString(),
      'attemptDurationMs': attemptDuration.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
      'deviceState': {
        'isConnected': deviceState?.isConnected,
        'signalStrength': deviceState?.signalStrength,
        'isInRange': deviceState?.isInRange,
      },
      'adapterState': adapterState?.toString(),
      'historicalSuccessRate': historicalPerformance?.successRate,
      'recommendations': recommendations,
    };
  }
}
