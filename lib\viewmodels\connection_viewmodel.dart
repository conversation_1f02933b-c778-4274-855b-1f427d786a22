import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../models/connection_state.dart';
import '../services/blufi_service.dart';

/// 连接管理视图模型
/// 负责管理设备连接状态和相关业务逻辑
class ConnectionViewModel extends ChangeNotifier {
  static final Logger _logger = Logger();

  final BluFiService _blufiService;

  // 连接状态
  BluFiServiceState _serviceState = BluFiServiceState.idle;
  BluFiDevice? _connectedDevice;
  WiFiConnectionState? _wifiConnectionState;

  // 连接统计
  DateTime? _connectionStartTime;
  Duration? _connectionDuration;
  Timer? _connectionTimer;

  // 错误信息
  String? _errorMessage;

  // 版本信息
  String? _deviceVersion;
  bool _isQueryingVersion = false;

  // 订阅管理
  StreamSubscription? _stateSubscription;
  StreamSubscription? _wifiStateSubscription;
  StreamSubscription? _errorSubscription;

  ConnectionViewModel(this._blufiService);

  // Getters - 连接状态
  BluFiServiceState get serviceState => _serviceState;
  BluFiDevice? get connectedDevice => _connectedDevice;
  WiFiConnectionState? get wifiConnectionState => _wifiConnectionState;
  String? get errorMessage => _errorMessage;

  // Getters - 版本信息
  String? get deviceVersion => _deviceVersion;
  bool get isQueryingVersion => _isQueryingVersion;

  // Getters - 连接统计
  DateTime? get connectionStartTime => _connectionStartTime;
  Duration? get connectionDuration => _connectionDuration;

  // Getters - 状态检查
  bool get isConnected =>
      _serviceState == BluFiServiceState.ready ||
      _serviceState == BluFiServiceState.configuring;
  bool get isConnecting =>
      _serviceState == BluFiServiceState.connecting ||
      _serviceState == BluFiServiceState.negotiating;
  bool get isIdle => _serviceState == BluFiServiceState.idle;
  bool get hasError => _serviceState == BluFiServiceState.error;
  bool get canDisconnect => isConnected || isConnecting;

  /// 初始化视图模型
  Future<void> initialize() async {
    try {
      _logger.i('Initializing connection view model');

      _setupEventListeners();
      _updateServiceState(_blufiService.state);
      _updateConnectedDevice(_blufiService.connectedDevice);

      _logger.i('Connection view model initialized');
    } catch (e) {
      _logger.e('Failed to initialize connection view model: $e');
      _setError('初始化失败: $e');
    }
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听服务状态
    _stateSubscription = _blufiService.stateStream.listen(
      (state) {
        _updateServiceState(state);
      },
      onError: (error) {
        _logger.e('State stream error: $error');
        _setError('状态监听错误: $error');
      },
    );

    // 监听WiFi连接状态
    _wifiStateSubscription = _blufiService.wifiStateStream.listen(
      (state) {
        _wifiConnectionState = state;
        _logger.d('WiFi state updated: ${state.staStateDescription}');
        notifyListeners();
      },
      onError: (error) {
        _logger.e('WiFi state stream error: $error');
        _setError('WiFi状态监听错误: $error');
      },
    );

    // 监听错误
    _errorSubscription = _blufiService.errorStream.listen(
      (error) {
        _logger.w('BluFi service error: $error');
        _setError(error);
      },
    );
  }

  /// 更新服务状态
  void _updateServiceState(BluFiServiceState newState) {
    final oldState = _serviceState;
    _serviceState = newState;

    // 更新连接设备信息
    _updateConnectedDevice(_blufiService.connectedDevice);

    // 处理状态变化
    _handleStateChange(oldState, newState);

    _logger.d('Service state changed: $oldState -> $newState');
    notifyListeners();
  }

  /// 更新连接的设备
  void _updateConnectedDevice(BluFiDevice? device) {
    if (_connectedDevice != device) {
      _connectedDevice = device;

      if (device != null) {
        _startConnectionTimer();
      } else {
        _stopConnectionTimer();
      }
    }
  }

  /// 处理状态变化
  void _handleStateChange(
      BluFiServiceState oldState, BluFiServiceState newState) {
    // 连接建立
    if (newState == BluFiServiceState.ready &&
        oldState != BluFiServiceState.ready) {
      _connectionStartTime = DateTime.now();
      _clearError();
      _logger.i('Connection established');
    }

    // 连接断开
    if (newState == BluFiServiceState.idle &&
        oldState != BluFiServiceState.idle) {
      _connectionStartTime = null;
      _connectionDuration = null;
      _logger.i('Connection terminated');
    }

    // 错误状态
    if (newState == BluFiServiceState.error) {
      _logger.w('Service entered error state');
    }
  }

  /// 开始连接计时器
  void _startConnectionTimer() {
    _stopConnectionTimer();

    _connectionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_connectionStartTime != null) {
        _connectionDuration = DateTime.now().difference(_connectionStartTime!);
        notifyListeners();
      }
    });
  }

  /// 停止连接计时器
  void _stopConnectionTimer() {
    _connectionTimer?.cancel();
    _connectionTimer = null;
  }

  /// 断开连接
  Future<void> disconnect() async {
    if (!canDisconnect) {
      _logger.w('Cannot disconnect: current state is $_serviceState');
      return;
    }

    try {
      _logger.i('Disconnecting from device');
      _clearError();

      await _blufiService.disconnect();
    } catch (e) {
      _logger.e('Failed to disconnect: $e');
      _setError('断开连接失败: $e');
    }
  }

  /// 重新连接
  Future<bool> reconnect() async {
    if (_connectedDevice == null) {
      _logger.w('No device to reconnect to');
      _setError('没有可重连的设备');
      return false;
    }

    try {
      _logger.i('Reconnecting to device: ${_connectedDevice!.name}');
      _clearError();

      // 先断开现有连接
      if (canDisconnect) {
        await disconnect();
        // 等待断开完成
        await Future.delayed(const Duration(seconds: 1));
      }

      // 重新连接
      final success = await _blufiService.connectToDevice(_connectedDevice!);

      if (!success) {
        _setError('重连失败');
      }

      return success;
    } catch (e) {
      _logger.e('Failed to reconnect: $e');
      _setError('重连失败: $e');
      return false;
    }
  }

  /// 获取连接状态描述
  String get connectionStatusDescription {
    switch (_serviceState) {
      case BluFiServiceState.idle:
        return '未连接';
      case BluFiServiceState.scanning:
        return '扫描中';
      case BluFiServiceState.connecting:
        return '连接中';
      case BluFiServiceState.negotiating:
        return '密钥协商中';
      case BluFiServiceState.ready:
        return '已连接';
      case BluFiServiceState.configuring:
        return '配置中';
      case BluFiServiceState.error:
        return '连接错误';
      default:
        return '未知状态';
    }
  }

  /// 获取连接时长描述
  String get connectionDurationDescription {
    if (_connectionDuration == null) return '未连接';

    final duration = _connectionDuration!;
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours}小时${minutes}分钟${seconds}秒';
    } else if (minutes > 0) {
      return '${minutes}分钟${seconds}秒';
    } else {
      return '${seconds}秒';
    }
  }

  /// 获取设备信息
  Map<String, String> get deviceInfo {
    if (_connectedDevice == null) return {};

    return {
      '设备名称': _connectedDevice!.name,
      '设备ID': _connectedDevice!.deviceId,
      '信号强度':
          '${_connectedDevice!.rssi} dBm (${_connectedDevice!.rssiDescription})',
      '连接状态': connectionStatusDescription,
      '连接时长': connectionDurationDescription,
    };
  }

  /// 获取WiFi信息
  Map<String, String> get wifiInfo {
    if (_wifiConnectionState == null) return {};

    final info = <String, String>{
      '操作模式': _wifiConnectionState!.opModeDescription,
      'Station状态': _wifiConnectionState!.staStateDescription,
    };

    if (_wifiConnectionState!.staSsid != null) {
      info['连接的网络'] = _wifiConnectionState!.staSsid!;
    }

    if (_wifiConnectionState!.staIp != null) {
      info['IP地址'] = _wifiConnectionState!.staIp!;
    }

    if (_wifiConnectionState!.staBssid != null) {
      info['BSSID'] = _wifiConnectionState!.staBssid!;
    }

    if (_wifiConnectionState!.rssi != null) {
      info['WiFi信号'] =
          '${_wifiConnectionState!.rssi} dBm (${_wifiConnectionState!.rssiDescription})';
    }

    if (_wifiConnectionState!.softApConnCount > 0) {
      info['热点连接数'] = '${_wifiConnectionState!.softApConnCount}';
    }

    return info;
  }

  /// 检查WiFi是否已连接
  bool get isWifiConnected {
    return _wifiConnectionState?.isStaConnected ?? false;
  }

  /// 检查是否有热点连接
  bool get hasSoftApConnections {
    return _wifiConnectionState?.hasSoftApConnections ?? false;
  }

  /// 获取连接质量评分 (0-100)
  int get connectionQuality {
    if (!isConnected || _connectedDevice == null) return 0;

    final rssi = _connectedDevice!.rssi;
    if (rssi >= -50) return 100;
    if (rssi >= -60) return 80;
    if (rssi >= -70) return 60;
    if (rssi >= -80) return 40;
    if (rssi >= -90) return 20;
    return 10;
  }

  /// 重置状态
  void reset() {
    _logger.i('Resetting connection view model');

    _serviceState = BluFiServiceState.idle;
    _connectedDevice = null;
    _wifiConnectionState = null;
    _connectionStartTime = null;
    _connectionDuration = null;

    _stopConnectionTimer();
    _clearError();

    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// 公开的清除错误方法
  void clearError() {
    _clearError();
  }

  /// 查询设备版本信息
  Future<bool> queryDeviceVersion() async {
    if (!isConnected) {
      _setError('设备未连接，无法查询版本信息');
      return false;
    }

    if (_isQueryingVersion) {
      _logger.w('Version query already in progress');
      return false;
    }

    try {
      _isQueryingVersion = true;
      _clearError();
      notifyListeners();

      _logger.i('Querying device version');

      // 调用BluFi服务查询版本
      final version = await _blufiService.queryDeviceVersion();

      if (version != null) {
        _deviceVersion = version;
        _logger.i('Device version received: $version');
        return true;
      } else {
        _setError('获取版本信息失败');
        return false;
      }
    } catch (e) {
      _logger.e('Error querying device version: $e');
      _setError('查询版本信息时发生错误: $e');
      return false;
    } finally {
      _isQueryingVersion = false;
      notifyListeners();
    }
  }

  /// 刷新WiFi状态
  Future<bool> refreshWifiStatus() async {
    if (!isConnected) {
      _setError('设备未连接，无法刷新WiFi状态');
      return false;
    }

    try {
      _clearError();
      _logger.i('Refreshing WiFi status');

      // 调用BluFi服务刷新WiFi状态
      final success = await _blufiService.refreshWifiStatus();

      if (!success) {
        _setError('刷新WiFi状态失败');
      }

      return success;
    } catch (e) {
      _logger.e('Error refreshing WiFi status: $e');
      _setError('刷新WiFi状态时发生错误: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _logger.i('Disposing connection view model');

    _stateSubscription?.cancel();
    _wifiStateSubscription?.cancel();
    _errorSubscription?.cancel();

    _stopConnectionTimer();

    super.dispose();
  }
}
