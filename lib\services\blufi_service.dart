import 'dart:async';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../models/wifi_config.dart';
import '../models/connection_state.dart';
import 'bluetooth_service.dart';
import 'blufi_protocol.dart';
import 'blufi_security_manager.dart';
import 'permission_service.dart'; // 只用于权限检查

/// BluFi服务状态
enum BluFiServiceState {
  idle, // 空闲状态
  scanning, // 扫描中
  connecting, // 连接中
  negotiating, // 密钥协商中
  ready, // 准备就绪
  configuring, // 配置中
  error, // 错误状态
}

/// BluFi集成服务
/// 整合蓝牙服务和BluFi协议服务，提供完整的BluFi功能
class BluFiService {
  static final Logger _logger = Logger();

  final BluetoothService _bluetoothService = BluetoothService();
  final BluFiProtocolService _protocolService = BluFiProtocolService();
  final PermissionService _permissionService = PermissionService();

  // 服务状态
  BluFiServiceState _state = BluFiServiceState.idle;

  // 订阅管理
  StreamSubscription? _bluetoothDataSubscription;
  StreamSubscription? _bluetoothConnectionSubscription;
  StreamSubscription? _bluetoothErrorSubscription;

  // 事件流控制器
  final StreamController<BluFiServiceState> _stateController =
      StreamController<BluFiServiceState>.broadcast();
  final StreamController<List<BluFiDevice>> _devicesController =
      StreamController<List<BluFiDevice>>.broadcast();
  final StreamController<WiFiConnectionState> _wifiStateController =
      StreamController<WiFiConnectionState>.broadcast();
  final StreamController<List<String>> _wifiListController =
      StreamController<List<String>>.broadcast();
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // 公开的流
  Stream<BluFiServiceState> get stateStream => _stateController.stream;
  Stream<List<BluFiDevice>> get devicesStream => _devicesController.stream;
  Stream<WiFiConnectionState> get wifiStateStream =>
      _wifiStateController.stream;
  Stream<List<String>> get wifiListStream => _wifiListController.stream;
  Stream<String> get errorStream => _errorController.stream;
  Stream<String> get customDataStream => _protocolService.customDataStream;
  Stream<String> get versionResponseStream =>
      _protocolService.versionResponseStream;
  Stream<bool> get configureResponseStream =>
      _protocolService.configureResponseStream;
  Stream<bool> get securityResponseStream =>
      _protocolService.securityResponseStream;

  /// 获取当前服务状态
  BluFiServiceState get state => _state;

  /// 获取当前连接的设备
  BluFiDevice? get connectedDevice => _bluetoothService.connectedDevice;

  /// 获取安全管理器
  BluFiSecurityManager? getSecurityManager() {
    try {
      return _protocolService.securityManager;
    } catch (e) {
      _logger.e('Failed to get security manager: $e');
      return null;
    }
  }

  /// 检查蓝牙权限
  Future<bool> checkBluetoothPermissions() async {
    return await _permissionService.checkBluetoothPermissions();
  }

  /// 请求蓝牙权限
  Future<bool> requestBluetoothPermissions() async {
    final granted = await _permissionService.requestBluetoothPermissions();

    if (!granted) {
      _errorController.add('蓝牙权限被拒绝，无法使用BluFi功能');
    }

    return granted;
  }

  /// 获取详细权限状态
  Future<Map<String, String>> getPermissionStatus() async {
    return await _permissionService.getDetailedPermissionStatus();
  }

  /// 打开应用设置
  Future<bool> openAppSettings() async {
    return await _permissionService.openAppSettings();
  }

  /// 初始化服务
  Future<bool> initialize() async {
    try {
      _logger.i('Initializing BluFi service');

      // 验证蓝牙权限是否已获取（权限获取在main.dart中处理）
      if (!await checkBluetoothPermissions()) {
        _logger.e('Bluetooth permissions not granted');
        _updateState(BluFiServiceState.error);
        _errorController.add('蓝牙权限未获取，请重启应用');
        return false;
      }

      // 初始化蓝牙服务
      if (!await _bluetoothService.initialize()) {
        _updateState(BluFiServiceState.error);
        return false;
      }

      // 初始化协议服务
      if (!await _protocolService.initialize()) {
        _updateState(BluFiServiceState.error);
        return false;
      }

      // 设置事件监听
      _setupEventListeners();

      _updateState(BluFiServiceState.idle);
      _logger.i('BluFi service initialized successfully');
      return true;
    } catch (e) {
      _logger.e('Failed to initialize BluFi service: $e');
      _errorController.add('服务初始化失败: $e');
      _updateState(BluFiServiceState.error);
      return false;
    }
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听蓝牙设备发现
    _bluetoothService.devicesStream.listen(
      (devices) => _devicesController.add(devices),
      onError: (error) => _errorController.add('设备扫描错误: $error'),
    );

    // 监听蓝牙连接状态
    _bluetoothConnectionSubscription =
        _bluetoothService.connectionStateStream.listen(
      _handleBluetoothConnectionState,
      onError: (error) => _errorController.add('连接状态错误: $error'),
    );

    // 监听蓝牙数据接收
    _bluetoothDataSubscription = _bluetoothService.dataReceivedStream.listen(
      _handleReceivedData,
      onError: (error) => _errorController.add('数据接收错误: $error'),
    );

    // 监听蓝牙错误
    _bluetoothErrorSubscription = _bluetoothService.errorStream.listen(
      (error) => _errorController.add(error),
    );

    // 监听协议服务事件
    _protocolService.wifiStateStream.listen(
      (state) => _wifiStateController.add(state),
      onError: (error) => _errorController.add('WiFi状态错误: $error'),
    );

    _protocolService.wifiListStream.listen(
      (list) => _wifiListController.add(list),
      onError: (error) => _errorController.add('WiFi列表错误: $error'),
    );

    _protocolService.errorStream.listen(
      (error) => _errorController.add(error),
    );
  }

  /// 开始扫描设备
  Future<void> startScan(
      {Duration timeout = const Duration(seconds: 10)}) async {
    if (_state == BluFiServiceState.scanning) {
      _logger.w('Already scanning');
      return;
    }

    try {
      _updateState(BluFiServiceState.scanning);
      await _bluetoothService.startScan(timeout: timeout);
      _logger.i('Device scan started');
    } catch (e) {
      _logger.e('Failed to start scan: $e');
      _errorController.add('开始扫描失败: $e');
      _updateState(BluFiServiceState.error);
    }
  }

  /// 停止扫描
  Future<void> stopScan() async {
    try {
      await _bluetoothService.stopScan();
      if (_state == BluFiServiceState.scanning) {
        _updateState(BluFiServiceState.idle);
      }
      _logger.i('Device scan stopped');
    } catch (e) {
      _logger.e('Failed to stop scan: $e');
    }
  }

  /// 连接到设备
  Future<bool> connectToDevice(BluFiDevice device) async {
    if (_state == BluFiServiceState.connecting ||
        _state == BluFiServiceState.negotiating ||
        _state == BluFiServiceState.ready) {
      _logger.w('Already connecting or connected');
      return false;
    }

    try {
      _updateState(BluFiServiceState.connecting);
      _logger.i('Connecting to device: ${device.name}');

      // 重置协议服务状态（包括序列号）
      _protocolService.reset();
      _logger.i('Protocol service reset for new connection');

      // 重新初始化协议服务（包括安全管理器）
      if (!await _protocolService.initialize()) {
        _logger.e('Failed to reinitialize protocol service after reset');
        _updateState(BluFiServiceState.error);
        return false;
      }
      _logger.i('Protocol service reinitialized after reset');

      // 连接蓝牙设备
      if (!await _bluetoothService.connectToDevice(device)) {
        _updateState(BluFiServiceState.error);
        return false;
      }

      return true; // 连接成功后会通过状态监听自动进行密钥协商
    } catch (e) {
      _logger.e('Failed to connect to device: $e');
      _errorController.add('连接设备失败: $e');
      _updateState(BluFiServiceState.error);
      return false;
    }
  }

  /// 配置WiFi
  Future<bool> configureWiFi(WiFiConfig config) async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for WiFi configuration');
      _errorController.add('服务未准备就绪，无法配置WiFi');
      return false;
    }

    try {
      _updateState(BluFiServiceState.configuring);
      _logger.i('Configuring WiFi: ${config.modeDescription}');

      // 生成配置帧
      final configFrames = await _protocolService.configureWiFi(config);

      // 发送配置数据
      for (final frameData in configFrames) {
        if (!await _bluetoothService.sendData(frameData)) {
          _errorController.add('发送配置数据失败');
          _updateState(BluFiServiceState.error);
          return false;
        }

        // 短暂延迟以确保数据发送完成
        await Future.delayed(const Duration(milliseconds: 150));
      }
      _updateState(BluFiServiceState.ready);
      _logger.i('WiFi configuration sent successfully');
      return true;
    } catch (e) {
      _logger.e('Failed to configure WiFi: $e');
      _errorController.add('WiFi配置失败: $e');
      _updateState(BluFiServiceState.error);
      return false;
    }
  }

  /// 获取WiFi状态
  Future<bool> getWiFiStatus() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready');
      return false;
    }

    try {
      final statusFrame = await _protocolService.getWiFiStatus();
      return await _bluetoothService.sendData(statusFrame);
    } catch (e) {
      _logger.e('Failed to get WiFi status: $e');
      _errorController.add('获取WiFi状态失败: $e');
      return false;
    }
  }

  /// 获取WiFi列表
  Future<bool> getWiFiList() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready');
      return false;
    }

    try {
      final listFrame = await _protocolService.getWiFiList();
      return await _bluetoothService.sendData(listFrame);
    } catch (e) {
      _logger.e('Failed to get WiFi list: $e');
      _errorController.add('获取WiFi列表失败: $e');
      return false;
    }
  }

  /// 查询设备版本信息
  Future<String?> queryDeviceVersion() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for version query');
      return null;
    }

    try {
      _logger.i('Querying device version');

      // 清除之前的版本信息
      _protocolService.clearReceivedVersion();

      final versionFrame = await _protocolService.getDeviceVersion();

      if (await _bluetoothService.sendData(versionFrame)) {
        // 等待版本响应，使用更合理的超时机制
        const maxWaitTime = Duration(seconds: 5);
        const checkInterval = Duration(milliseconds: 100);
        int attempts =
            maxWaitTime.inMilliseconds ~/ checkInterval.inMilliseconds;

        for (int i = 0; i < attempts; i++) {
          await Future.delayed(checkInterval);

          final version = _protocolService.getReceivedVersion();
          if (version != null) {
            _logger.i('Device version received: $version');
            return version;
          }
        }

        _logger.w('Version query timeout - no response received');
        return null;
      } else {
        _logger.e('Failed to send version query frame');
        return null;
      }
    } catch (e) {
      _logger.e('Failed to query device version: $e');
      _errorController.add('查询设备版本失败: $e');
      return null;
    }
  }

  /// 刷新WiFi状态
  Future<bool> refreshWifiStatus() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for WiFi status refresh');
      return false;
    }

    try {
      _logger.i('Refreshing WiFi status');
      return await getWiFiStatus();
    } catch (e) {
      _logger.e('Failed to refresh WiFi status: $e');
      _errorController.add('刷新WiFi状态失败: $e');
      return false;
    }
  }

  /// 断开WiFi连接
  Future<bool> disconnectWifi() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for WiFi disconnect');
      return false;
    }

    try {
      _logger.i('Disconnecting WiFi');
      final disconnectFrame = await _protocolService.disconnectWifi();
      return await _bluetoothService.sendData(disconnectFrame);
    } catch (e) {
      _logger.e('Failed to disconnect WiFi: $e');
      _errorController.add('断开WiFi失败: $e');
      return false;
    }
  }

  /// 发送自定义数据
  Future<bool> sendCustomData(List<int> customData) async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for custom data');
      return false;
    }

    try {
      _logger.i('Sending custom data: ${customData.length} bytes');
      final customFrame = await _protocolService.sendCustomData(customData);
      return await _bluetoothService.sendData(customFrame);
    } catch (e) {
      _logger.e('Failed to send custom data: $e');
      _errorController.add('发送自定义数据失败: $e');
      return false;
    }
  }

  /// 协商安全密钥 - 对应Android的negotiateSecurity
  Future<bool> negotiateSecurity() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for security negotiation');
      return false;
    }

    try {
      _logger.i('Starting security negotiation');

      // 简化的密钥协商：直接调用协议服务
      final negotiationFrames = await _protocolService.startKeyNegotiation();

      // 发送协商帧
      for (final frameData in negotiationFrames) {
        if (!await _bluetoothService.sendData(frameData)) {
          _errorController.add('密钥协商数据发送失败');
          return false;
        }
      }

      _logger.i('Security negotiation initiated');
      return true;
    } catch (e) {
      _logger.e('Failed to negotiate security: $e');
      _errorController.add('安全协商失败: $e');
      return false;
    }
  }

  /// 扫描WiFi网络 - 对应Android的requestDeviceWifiScan
  Future<bool> scanWiFiNetworks() async {
    if (_state != BluFiServiceState.ready) {
      _logger.w('Service not ready for WiFi scan');
      return false;
    }

    try {
      _logger.i('Requesting device WiFi scan');
      final scanFrame = await _protocolService.getWiFiList();
      return await _bluetoothService.sendData(scanFrame);
    } catch (e) {
      _logger.e('Failed to scan WiFi networks: $e');
      _errorController.add('WiFi扫描失败: $e');
      return false;
    }
  }

  /// 断开BLE连接
  Future<bool> disconnectBle() async {
    if (_state == BluFiServiceState.idle) {
      _logger.w('Already disconnected');
      return true;
    }

    try {
      _logger.i('Disconnecting BLE');
      if (_state == BluFiServiceState.ready) {
        final disconnectFrame = await _protocolService.disconnectBle();
        await _bluetoothService.sendData(disconnectFrame);
      }

      await disconnect();
      return true;
    } catch (e) {
      _logger.e('Failed to disconnect BLE: $e');
      _errorController.add('断开BLE连接失败: $e');
      return false;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    try {
      _logger.i('Disconnecting from device');

      await _bluetoothService.disconnect();
      _protocolService.reset();

      // 不手动更新状态，让蓝牙连接状态变化自动触发状态更新
      // _updateState(BluFiServiceState.idle); // 移除手动状态更新
      _logger.i(
          'Device disconnect initiated, waiting for connection state change');
    } catch (e) {
      _logger.e('Failed to disconnect: $e');
      _errorController.add('断开连接失败: $e');
    }
  }

  /// 处理蓝牙连接状态变化
  void _handleBluetoothConnectionState(
      BluetoothConnectionState connectionState) {
    _logger.i('Bluetooth connection state changed: $connectionState');

    switch (connectionState) {
      case BluetoothConnectionState.connected:
        _logger.i('Bluetooth connected, ready for BluFi operations');
        // 连接成功后直接进入ready状态，加密完全由用户决定
        _updateState(BluFiServiceState.ready);
        break;
      case BluetoothConnectionState.disconnected:
        _logger.i('Bluetooth disconnected');
        // 总是更新状态为idle，确保UI同步
        _updateState(BluFiServiceState.idle);
        break;
      case BluetoothConnectionState.connecting:
        _logger.i('Bluetooth connecting...');
        _updateState(BluFiServiceState.connecting);
        break;
      case BluetoothConnectionState.disconnecting:
        _logger.i('Bluetooth disconnecting...');
        break;
    }
  }

  /// 处理接收到的数据
  Future<void> _handleReceivedData(Uint8List data) async {
    try {
      _logger.d('Received data: ${data.length} bytes, current state: $_state');

      // 通过协议服务处理数据
      final responses = await _protocolService.processReceivedData(data);
      _logger.d('Protocol service returned ${responses.length} responses');

      // 发送响应数据
      for (final response in responses) {
        final sent = await _bluetoothService.sendData(response);
        _logger.d('Response sent: $sent, ${response.length} bytes');
      }

      // 检查协议状态变化
      final protocolState = _protocolService.state;
      _logger.d('Protocol state: $protocolState');

      if (_state == BluFiServiceState.negotiating &&
          protocolState == BluFiProtocolState.connected) {
        _updateState(BluFiServiceState.ready);
        _logger.i('Key negotiation completed, service ready');
      }
    } catch (e) {
      _logger.e('Failed to handle received data: $e');
      _errorController.add('数据处理失败: $e');
      _updateState(BluFiServiceState.error);
    }
  }

  /// 更新服务状态
  void _updateState(BluFiServiceState newState) {
    if (_state != newState) {
      final oldState = _state;
      _state = newState;
      _stateController.add(newState);
      _logger.d('Service state changed from $oldState to: $newState');
    } else {
      _logger.i('[BluFiService] State update ignored: already $newState');
    }
  }

  /// 获取已发现的设备列表
  List<BluFiDevice> get discoveredDevices =>
      _bluetoothService.discoveredDevices;

  /// 清除已发现的设备
  void clearDiscoveredDevices() {
    _bluetoothService.clearDiscoveredDevices();
  }

  /// 重置服务
  void reset() {
    _protocolService.reset();
    _bluetoothService.clearDiscoveredDevices();
    _updateState(BluFiServiceState.idle);
    _logger.i('BluFi service reset');
  }

  /// 释放资源
  void dispose() {
    _bluetoothDataSubscription?.cancel();
    _bluetoothConnectionSubscription?.cancel();
    _bluetoothErrorSubscription?.cancel();

    _bluetoothService.dispose();
    _protocolService.dispose();

    _stateController.close();
    _devicesController.close();
    _wifiStateController.close();
    _wifiListController.close();
    _errorController.close();

    _logger.i('BluFi service disposed');
  }
}
