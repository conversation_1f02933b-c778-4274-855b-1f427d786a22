import 'dart:async';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import '../models/blufi_frame.dart';
import '../models/wifi_config.dart';
import '../models/connection_state.dart';
import '../utils/blufi_constants.dart';
import '../utils/error_messages.dart';
import '../utils/extensions.dart';
import 'blufi_security_manager.dart';
import 'frame_parser.dart';

/// BluFi协议状态
enum BluFiProtocolState {
  idle, // 空闲状态
  negotiating, // 密钥协商中
  configuring, // 配置中
  connected, // 已连接
  error, // 错误状态
}

/// BluFi协议服务
/// 实现完整的BluFi协议处理逻辑
class BluFiProtocolService {
  static final Logger _logger = Logger();

  final BluFiSecurityManager _securityManager = BluFiSecurityManager();
  final FrameParser _frameParser = FrameParser();

  // 协议状态
  BluFiProtocolState _state = BluFiProtocolState.idle;

  // 安全配置
  bool _dataFrameEncrypted = false;
  bool _dataFrameChecksum = false;
  bool _controlFrameEncrypted = false;
  bool _controlFrameChecksum = false;

  // 等待ACK的帧
  final Map<int, Completer<bool>> _pendingAcks = {};
  final Map<int, Timer> _ackTimers = {};

  // 接收到的数据
  String? _receivedVersion;

  // 事件流控制器
  final StreamController<WiFiConnectionState> _wifiStateController =
      StreamController<WiFiConnectionState>.broadcast();
  final StreamController<List<String>> _wifiListController =
      StreamController<List<String>>.broadcast();
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  final StreamController<String> _customDataController =
      StreamController<String>.broadcast();
  final StreamController<String> _versionResponseController =
      StreamController<String>.broadcast();
  final StreamController<bool> _configureResponseController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _securityResponseController =
      StreamController<bool>.broadcast();

  // 公开的流
  Stream<WiFiConnectionState> get wifiStateStream =>
      _wifiStateController.stream;
  Stream<List<String>> get wifiListStream => _wifiListController.stream;
  Stream<String> get errorStream => _errorController.stream;
  Stream<String> get customDataStream => _customDataController.stream;
  Stream<String> get versionResponseStream => _versionResponseController.stream;
  Stream<bool> get configureResponseStream =>
      _configureResponseController.stream;
  Stream<bool> get securityResponseStream => _securityResponseController.stream;

  /// 获取当前协议状态
  BluFiProtocolState get state => _state;

  /// 获取安全管理器（用于用户控制加密）
  BluFiSecurityManager get securityManager => _securityManager;

  /// 初始化协议
  Future<bool> initialize() async {
    try {
      await _securityManager.initialize();
      _frameParser.resetSequence();
      _state = BluFiProtocolState.idle;
      _logger.i('BluFi protocol initialized');
      return true;
    } catch (e) {
      _logger.e('Failed to initialize BluFi protocol: $e');
      _state = BluFiProtocolState.error;
      return false;
    }
  }

  /// 开始密钥协商
  Future<List<Uint8List>> startKeyNegotiation() async {
    _state = BluFiProtocolState.negotiating;

    // 使用安全管理器获取公钥数据
    final publicKeyData = await _securityManager.startKeyNegotiation();
    if (publicKeyData == null) {
      throw StateError('Failed to start key negotiation');
    }

    // 使用帧解析器创建协商帧，确保序列号正确管理
    final negotiationFrame = _frameParser.createNegotiationDataFrame(
      publicKeyData,
      encrypted: false, // 协商数据不加密
      hasChecksum: false, // 协商数据不校验
    );

    _logger.i(
        'Starting key negotiation with sequence: ${negotiationFrame.sequence}');
    return [negotiationFrame.toBytes()];
  }

  /// 处理接收到的数据
  Future<List<Uint8List>> processReceivedData(Uint8List data) async {
    final responses = <Uint8List>[];

    try {
      final frames = _frameParser.parseReceivedData(data);

      for (final frame in frames) {
        final response = await _processFrame(frame);
        if (response != null) {
          responses.addAll(response);
        }
      }
    } catch (e) {
      _logger.e('Error processing received data: $e');
      _errorController.add('数据处理错误: $e');
    }

    return responses;
  }

  /// 配置WiFi
  Future<List<Uint8List>> configureWiFi(WiFiConfig config) async {
    // 允许在任何非错误状态下配置WiFi
    // 用户可以选择是否进行密钥协商
    if (_state == BluFiProtocolState.error) {
      throw StateError('Protocol in error state, cannot configure');
    }

    _logger.i('Configuring WiFi in state: $_state');

    _state = BluFiProtocolState.configuring;
    final frames = <Uint8List>[];

    try {
      // 1. 设置安全模式
      final securityFrame = _frameParser.createSetSecurityModeFrame(
        controlFrameChecksum: _controlFrameChecksum,
        controlFrameEncrypt: _controlFrameEncrypted,
        dataFrameChecksum: _dataFrameChecksum,
        dataFrameEncrypt: _dataFrameEncrypted,
      );
      frames.add(securityFrame.toBytes());

      // 2. 设置WiFi操作模式
      final opModeFrame = _frameParser.createSetOpModeFrame(config.opMode);
      frames.add(opModeFrame.toBytes());

      // 3. 根据模式发送配置数据
      if (config.stationConfig != null) {
        frames.addAll(await _createStationConfigFrames(config.stationConfig!));
      }

      if (config.softApConfig != null) {
        frames.addAll(await _createSoftApConfigFrames(config.softApConfig!));
      }

      // 4. 发送连接命令
      final connectFrame = _frameParser.createConnectWifiFrame();
      frames.add(connectFrame.toBytes());

      _logger.i('WiFi configuration frames created');

      // 发送配置响应事件到UI（表示配置命令已发送）
      _configureResponseController.add(true);

      return frames;
    } catch (e) {
      _logger.e('Error creating WiFi configuration: $e');
      _errorController.add('WiFi配置错误: $e');
      _state = BluFiProtocolState.error;
      rethrow;
    }
  }

  /// 获取WiFi状态
  Future<Uint8List> getWiFiStatus() async {
    final frame = _frameParser.createGetWifiStatusFrame();
    return frame.toBytes();
  }

  /// 获取WiFi列表
  Future<Uint8List> getWiFiList() async {
    final frame = _frameParser.createGetWifiListFrame();
    return frame.toBytes();
  }

  /// 处理单个帧
  Future<List<Uint8List>?> _processFrame(BluFiFrame frame) async {
    _logger.d('Processing frame: ${frame.typeDescription}');

    // 处理ACK帧
    if (frame.isControlFrame &&
        frame.subtype == BluFiConstants.ctrlSubtypeAck) {
      _handleAckFrame(frame);
      return null;
    }

    // 解密数据（如果需要）
    Uint8List frameData = frame.data;
    if (frame.isEncrypted) {
      final decrypted =
          _securityManager.decryptData(frame.data, frame.sequence);
      if (decrypted != null) {
        frameData = decrypted;
      } else {
        _logger.w('Failed to decrypt frame data');
        return null;
      }
    }

    // 处理分片
    final completeData = _frameParser.handleFragmentedFrame(frame);
    if (completeData == null && frame.isFragment) {
      return null; // 等待更多分片
    }

    final dataToProcess = completeData ?? frameData;

    // 根据帧类型处理
    if (frame.isDataFrame) {
      return await _processDataFrame(frame.subtype, dataToProcess);
    } else {
      return await _processControlFrame(frame.subtype, dataToProcess);
    }
  }

  /// 处理数据帧
  Future<List<Uint8List>?> _processDataFrame(
      int subtype, Uint8List data) async {
    switch (subtype) {
      case BluFiConstants.dataSubtypeNegotiateData:
        return await _handleNegotiationData(data);

      case BluFiConstants.dataSubtypeWifiConnectionState:
        _handleWifiConnectionState(data);
        break;

      case BluFiConstants.dataSubtypeWifiList:
        _handleWifiList(data);
        break;

      case BluFiConstants.dataSubtypeError:
        _handleError(data);
        break;

      case BluFiConstants.dataSubtypeVersion:
        _handleVersion(data);
        break;

      case BluFiConstants.dataSubtypeCustomData:
        _handleCustomData(data);
        break;

      case BluFiConstants.dataSubtypeMaxReconnectTime:
        _handleMaxReconnectTime(data);
        break;

      case BluFiConstants.dataSubtypeWifiEndReason:
        _handleWifiEndReason(data);
        break;

      case BluFiConstants.dataSubtypeWifiEndRssi:
        _handleWifiEndRssi(data);
        break;
    }

    return null;
  }

  /// 处理控制帧
  Future<List<Uint8List>?> _processControlFrame(
      int subtype, Uint8List data) async {
    // 大多数控制帧不需要特殊处理
    return null;
  }

  /// 处理密钥协商数据
  Future<List<Uint8List>?> _handleNegotiationData(Uint8List data) async {
    // 使用安全管理器处理协商数据
    final success = await _securityManager.handleNegotiationData(data);

    if (success) {
      _state = BluFiProtocolState.connected;
      _logger.i('Key negotiation completed successfully');

      // 发送安全协商成功事件到UI
      _securityResponseController.add(true);
      return null;
    } else {
      _logger.e('Failed to handle negotiation data');
      _state = BluFiProtocolState.error;
      _errorController.add('密钥协商失败');

      // 发送安全协商失败事件到UI
      _securityResponseController.add(false);
      return null;
    }
  }

  /// 处理WiFi连接状态
  void _handleWifiConnectionState(Uint8List data) {
    final state = _frameParser.parseWifiConnectionState(data);
    if (state != null) {
      _wifiStateController.add(state);
      _logger.i('WiFi state updated: ${state.staStateDescription}');
    }
  }

  /// 处理WiFi列表
  void _handleWifiList(Uint8List data) {
    final wifiList = <String>[];
    int offset = 0;

    while (offset < data.length) {
      if (offset + 1 >= data.length) break;

      final length = data[offset++];
      if (offset + length > data.length) break;

      final rssi = data[offset++].toSigned(8); // 转换为有符号整数
      final ssidBytes = data.sublist(offset, offset + length - 1);
      offset += length - 1;

      final ssid = String.fromCharCodes(ssidBytes);
      wifiList.add('$ssid (${rssi}dBm)');
    }

    _wifiListController.add(wifiList);
    _logger.i('WiFi list received: ${wifiList.length} networks');
  }

  /// 处理错误
  void _handleError(Uint8List data) {
    if (data.isNotEmpty) {
      final errorCode = data[0];
      final errorMessage = BluFiErrorMessages.getErrorMessage(errorCode);
      final severity = BluFiErrorMessages.getErrorSeverity(errorCode);

      _errorController.add(errorMessage);

      // 根据严重程度记录不同级别的日志
      switch (severity) {
        case ErrorSeverity.warning:
        case ErrorSeverity.recoverable:
          _logger.w(
              'BluFi error: $errorMessage (code: 0x${errorCode.toRadixString(16)})');
          break;
        case ErrorSeverity.critical:
        case ErrorSeverity.fatal:
          _logger.e(
              'BluFi critical error: $errorMessage (code: 0x${errorCode.toRadixString(16)})');
          break;
        case ErrorSeverity.unknown:
          _logger.w(
              'BluFi unknown error: $errorMessage (code: 0x${errorCode.toRadixString(16)})');
          break;
      }
    }
  }

  /// 处理版本信息
  void _handleVersion(Uint8List data) {
    if (data.length >= 2) {
      final majorVersion = data[0];
      final minorVersion = data[1];
      _receivedVersion = '$majorVersion.$minorVersion';
      _logger.i('Device version: $_receivedVersion');

      // 发送版本响应事件到UI
      _versionResponseController.add(_receivedVersion!);
    }
  }

  /// 处理自定义数据
  void _handleCustomData(Uint8List data) {
    _logger.i('Received custom data: ${data.length} bytes');

    // 将字节数据转换为字符串
    final customDataString = String.fromCharCodes(data);

    // 通过事件流发送自定义数据给应用层
    _customDataController.add(customDataString);

    _logger.i('Custom data forwarded to UI: $customDataString');
  }

  /// 处理最大重连时间
  void _handleMaxReconnectTime(Uint8List data) {
    if (data.length >= 4) {
      final maxReconnectTime = data.readUint32LE(0);
      _logger.i('Max reconnect time: ${maxReconnectTime}s');
    }
  }

  /// 处理WiFi连接结束原因
  void _handleWifiEndReason(Uint8List data) {
    if (data.isNotEmpty) {
      final reason = data[0];
      _logger.i('WiFi disconnect reason: $reason');
    }
  }

  /// 处理WiFi连接结束时的RSSI
  void _handleWifiEndRssi(Uint8List data) {
    if (data.isNotEmpty) {
      final rssi = data[0].toSigned(8); // 转换为有符号8位整数
      _logger.i('WiFi disconnect RSSI: ${rssi}dBm');
    }
  }

  /// 处理ACK帧
  void _handleAckFrame(BluFiFrame frame) {
    if (frame.data.length >= 2) {
      final ackedSequence = frame.data.readUint16LE(0);
      final completer = _pendingAcks.remove(ackedSequence);
      completer?.complete(true);
    }
  }

  /// 创建Station配置帧
  Future<List<Uint8List>> _createStationConfigFrames(
      StationConfig config) async {
    final frames = <Uint8List>[];

    // SSID
    final ssidFrame = _frameParser.createStaSsidFrame(
      config.ssid,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(ssidFrame));

    // 密码
    final passwordFrame = _frameParser.createStaPasswordFrame(
      config.password,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(passwordFrame));

    return frames;
  }

  /// 创建SoftAP配置帧
  Future<List<Uint8List>> _createSoftApConfigFrames(SoftApConfig config) async {
    final frames = <Uint8List>[];

    // SSID
    final ssidFrame = _frameParser.createSoftApSsidFrame(
      config.ssid,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(ssidFrame));

    // 密码
    final passwordFrame = _frameParser.createSoftApPasswordFrame(
      config.password,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(passwordFrame));

    // 最大连接数
    final maxConnFrame = _frameParser.createSoftApMaxConnFrame(
      config.maxConnections,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(maxConnFrame));

    // 认证模式
    final authModeFrame = _frameParser.createSoftApAuthModeFrame(
      config.authMode,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(authModeFrame));

    // 信道
    final channelFrame = _frameParser.createSoftApChannelFrame(
      config.channel,
      encrypted: _dataFrameEncrypted,
      hasChecksum: _dataFrameChecksum,
    );
    frames.add(await _encryptFrameIfNeeded(channelFrame));

    return frames;
  }

  /// 如果需要则加密帧
  Future<Uint8List> _encryptFrameIfNeeded(BluFiFrame frame) async {
    if (frame.isEncrypted) {
      final encrypted =
          _securityManager.encryptData(frame.data, frame.sequence);
      if (encrypted != null) {
        final encryptedFrame = BluFiFrame(
          type: frame.type,
          subtype: frame.subtype,
          frameControl: frame.frameControl,
          sequence: frame.sequence,
          data: encrypted,
          checksum: frame.checksum,
        );
        return encryptedFrame.toBytes();
      }
    }

    return frame.toBytes();
  }

  /// 获取设备版本信息
  Future<Uint8List> getDeviceVersion() async {
    final frame = _frameParser.createGetVersionFrame();
    return await _encryptFrameIfNeeded(frame);
  }

  /// 获取接收到的版本信息
  String? getReceivedVersion() {
    return _receivedVersion;
  }

  /// 清除接收到的版本信息
  void clearReceivedVersion() {
    _receivedVersion = null;
  }

  /// 断开WiFi连接
  Future<Uint8List> disconnectWifi() async {
    final frame = _frameParser.createDisconnectWifiFrame();
    return await _encryptFrameIfNeeded(frame);
  }

  /// 发送自定义数据
  Future<Uint8List> sendCustomData(List<int> customData) async {
    final frame =
        _frameParser.createCustomDataFrame(Uint8List.fromList(customData));
    return await _encryptFrameIfNeeded(frame);
  }

  /// 断开BLE连接
  Future<Uint8List> disconnectBle() async {
    final frame = _frameParser.createDisconnectBleFrame();
    return await _encryptFrameIfNeeded(frame);
  }

  /// 重置协议状态
  void reset() {
    _state = BluFiProtocolState.idle;
    _securityManager.reset();
    _frameParser.resetSequence();
    _frameParser.clearFragmentCache();
    _pendingAcks.clear();

    _dataFrameEncrypted = false;
    _dataFrameChecksum = false;
    _controlFrameEncrypted = false;
    _controlFrameChecksum = false;

    // 清除接收到的数据
    _receivedVersion = null;

    _logger.i('BluFi protocol reset');
  }

  /// 释放资源
  void dispose() {
    // 取消所有ACK定时器
    for (final timer in _ackTimers.values) {
      timer.cancel();
    }
    _ackTimers.clear();

    _wifiStateController.close();
    _wifiListController.close();
    _errorController.close();
    _customDataController.close();
    _versionResponseController.close();
    _configureResponseController.close();
    _securityResponseController.close();
    reset();
  }
}
