import 'dart:typed_data';
import 'package:flutter/material.dart';

/// 字节数组扩展方法
extension Uint8ListExtension on Uint8List {
  /// 转换为十六进制字符串
  String toHexString() {
    return map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join(' ')
        .toUpperCase();
  }

  /// 计算CRC16校验和
  int calculateCrc16() {
    int crc = 0xFFFF;
    for (int byte in this) {
      crc ^= byte;
      for (int i = 0; i < 8; i++) {
        if ((crc & 0x0001) != 0) {
          crc = (crc >> 1) ^ 0xA001;
        } else {
          crc = crc >> 1;
        }
      }
    }
    return crc & 0xFFFF;
  }

  /// 从指定位置读取16位整数（小端序）
  int readUint16LE(int offset) {
    if (offset + 1 >= length) throw RangeError('Offset out of range');
    return this[offset] | (this[offset + 1] << 8);
  }

  /// 从指定位置读取32位整数（小端序）
  int readUint32LE(int offset) {
    if (offset + 3 >= length) throw RangeError('Offset out of range');
    return this[offset] |
        (this[offset + 1] << 8) |
        (this[offset + 2] << 16) |
        (this[offset + 3] << 24);
  }
}

/// 整数扩展方法
extension IntExtension on int {
  /// 转换为字节数组（小端序）
  Uint8List toUint8ListLE(int byteCount) {
    final bytes = Uint8List(byteCount);
    for (int i = 0; i < byteCount; i++) {
      bytes[i] = (this >> (i * 8)) & 0xFF;
    }
    return bytes;
  }

  /// 转换为16位字节数组（小端序）
  Uint8List toUint16LE() => toUint8ListLE(2);

  /// 转换为32位字节数组（小端序）
  Uint8List toUint32LE() => toUint8ListLE(4);

  /// 检查指定位是否设置
  bool isBitSet(int bitPosition) {
    return (this & (1 << bitPosition)) != 0;
  }

  /// 设置指定位
  int setBit(int bitPosition) {
    return this | (1 << bitPosition);
  }

  /// 清除指定位
  int clearBit(int bitPosition) {
    return this & ~(1 << bitPosition);
  }
}

/// 字符串扩展方法
extension StringExtension on String {
  /// 转换为UTF-8字节数组
  Uint8List toUtf8Bytes() {
    final List<int> codeUnits = [];
    for (int i = 0; i < length; i++) {
      final int codeUnit = codeUnitAt(i);
      if (codeUnit < 0x80) {
        codeUnits.add(codeUnit);
      } else if (codeUnit < 0x800) {
        codeUnits.add(0xC0 | (codeUnit >> 6));
        codeUnits.add(0x80 | (codeUnit & 0x3F));
      } else {
        codeUnits.add(0xE0 | (codeUnit >> 12));
        codeUnits.add(0x80 | ((codeUnit >> 6) & 0x3F));
        codeUnits.add(0x80 | (codeUnit & 0x3F));
      }
    }
    return Uint8List.fromList(codeUnits);
  }

  /// 从十六进制字符串转换为字节数组
  Uint8List fromHexString() {
    final String cleanHex = replaceAll(RegExp(r'[^0-9A-Fa-f]'), '');
    if (cleanHex.length % 2 != 0) {
      throw const FormatException('Invalid hex string length');
    }

    final List<int> bytes = [];
    for (int i = 0; i < cleanHex.length; i += 2) {
      final String hexByte = cleanHex.substring(i, i + 2);
      bytes.add(int.parse(hexByte, radix: 16));
    }
    return Uint8List.fromList(bytes);
  }

  /// 检查是否为有效的MAC地址
  bool isValidMacAddress() {
    final RegExp macRegex =
        RegExp(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$');
    return macRegex.hasMatch(this);
  }

  /// 检查是否为有效的IP地址
  bool isValidIpAddress() {
    final RegExp ipRegex = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
    if (!ipRegex.hasMatch(this)) return false;

    final List<String> parts = split('.');
    for (String part in parts) {
      final int value = int.tryParse(part) ?? -1;
      if (value < 0 || value > 255) return false;
    }
    return true;
  }
}

/// 颜色扩展方法
extension ColorExtension on Color {
  /// 转换为十六进制字符串
  String toHexString() {
    return '#${value.toRadixString(16).padLeft(8, '0').toUpperCase()}';
  }

  /// 获取对比色（黑色或白色）
  Color getContrastColor() {
    // 计算亮度
    final double luminance = (0.299 * red + 0.587 * green + 0.114 * blue) / 255;
    return luminance > 0.5 ? const Color(0xFF000000) : const Color(0xFFFFFFFF);
  }
}

/// DateTime扩展方法
extension DateTimeExtension on DateTime {
  /// 格式化为友好的时间字符串
  String toFriendlyString() {
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(this);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 格式化为标准时间字符串
  String toStandardString() {
    return '${year.toString().padLeft(4, '0')}-'
        '${month.toString().padLeft(2, '0')}-'
        '${day.toString().padLeft(2, '0')} '
        '${hour.toString().padLeft(2, '0')}:'
        '${minute.toString().padLeft(2, '0')}:'
        '${second.toString().padLeft(2, '0')}';
  }
}
