/// BluFi协议常量定义
/// 基于ESP-IDF v5.2.2 BluFi协议规范
class BluFiConstants {
  // GATT服务和特征值UUID
  static const String serviceUuid = "0000FFFF-0000-1000-8000-00805F9B34FB";
  static const String writeCharacteristicUuid =
      "0000FF01-0000-1000-8000-00805F9B34FB"; // 手机 -> ESP32
  static const String notifyCharacteristicUuid =
      "0000FF02-0000-1000-8000-00805F9B34FB"; // ESP32 -> 手机

  // 帧类型定义
  static const int frameTypeControl = 0x0; // 控制帧
  static const int frameTypeData = 0x1; // 数据帧

  // 帧控制字段位定义
  static const int frameCtrlEncrypted = 0x01; // 是否加密
  static const int frameCtrlChecksum = 0x02; // 是否包含校验和
  static const int frameCtrlDirection = 0x04; // 数据方向 (0=手机->ESP32, 1=ESP32->手机)
  static const int frameCtrlRequireAck = 0x08; // 是否需要ACK回复
  static const int frameCtrlFrag = 0x10; // 是否有后续数据分片

  // 控制帧子类型 (Type = 0x0)
  static const int ctrlSubtypeAck = 0x0; // ACK帧
  static const int ctrlSubtypeSetSecurityMode = 0x1; // 设置安全模式
  static const int ctrlSubtypeSetOpMode = 0x2; // 设置WiFi操作模式
  static const int ctrlSubtypeConnectWifi = 0x3; // 连接到AP
  static const int ctrlSubtypeDisconnectWifi = 0x4; // 断开AP连接
  static const int ctrlSubtypeGetWifiStatus = 0x5; // 获取WiFi状态
  static const int ctrlSubtypeDeauthSta = 0x6; // 断开STA设备
  static const int ctrlSubtypeGetVersion = 0x7; // 获取版本信息
  static const int ctrlSubtypeDisconnectBle = 0x8; // 断开BLE连接
  static const int ctrlSubtypeGetWifiList = 0x9; // 获取WiFi列表

  // 数据帧子类型 (Type = 0x1)
  static const int dataSubtypeNegotiateData = 0x0; // 协商数据
  static const int dataSubtypeStaBssid = 0x1; // STA模式BSSID
  static const int dataSubtypeStaSsid = 0x2; // STA模式SSID
  static const int dataSubtypeStaPassword = 0x3; // STA模式密码
  static const int dataSubtypeSoftapSsid = 0x4; // SoftAP模式SSID
  static const int dataSubtypeSoftapPassword = 0x5; // SoftAP模式密码
  static const int dataSubtypeSoftapMaxConnNum = 0x6; // SoftAP最大连接数
  static const int dataSubtypeSoftapAuthMode = 0x7; // SoftAP认证模式
  static const int dataSubtypeSoftapChannel = 0x8; // SoftAP信道
  static const int dataSubtypeUsername = 0x9; // 企业级加密用户名
  static const int dataSubtypeCaCert = 0xa; // CA证书
  static const int dataSubtypeClientCert = 0xb; // 客户端证书
  static const int dataSubtypeServerCert = 0xc; // 服务器证书
  static const int dataSubtypeClientPrivateKey = 0xd; // 客户端私钥
  static const int dataSubtypeServerPrivateKey = 0xe; // 服务器私钥
  static const int dataSubtypeWifiConnectionState = 0xf; // WiFi连接状态报告
  static const int dataSubtypeVersion = 0x10; // 版本信息
  static const int dataSubtypeWifiList = 0x11; // WiFi列表
  static const int dataSubtypeError = 0x12; // 错误报告
  static const int dataSubtypeCustomData = 0x13; // 自定义数据
  static const int dataSubtypeMaxReconnectTime = 0x14; // 最大重连时间
  static const int dataSubtypeWifiEndReason = 0x15; // WiFi连接结束原因
  static const int dataSubtypeWifiEndRssi = 0x16; // WiFi连接结束时的RSSI

  // WiFi操作模式
  static const int wifiModeNull = 0x0; // 空模式
  static const int wifiModeSta = 0x1; // Station模式
  static const int wifiModeAp = 0x2; // SoftAP模式
  static const int wifiModeApSta = 0x3; // SoftAP + Station模式

  // WiFi认证模式
  static const int wifiAuthOpen = 0x0; // 开放
  static const int wifiAuthWep = 0x1; // WEP
  static const int wifiAuthWpaPsk = 0x2; // WPA_PSK
  static const int wifiAuthWpa2Psk = 0x3; // WPA2_PSK
  static const int wifiAuthWpaWpa2Psk = 0x4; // WPA_WPA2_PSK

  // WiFi连接状态
  static const int wifiStateConnectedWithIp = 0x0; // 已连接并获得IP
  static const int wifiStateDisconnected = 0x1; // 已断开
  static const int wifiStateConnecting = 0x2; // 连接中
  static const int wifiStateConnectedNoIp = 0x3; // 已连接但无IP

  // 安全模式设置
  static const int securityModeNone = 0x0; // 无校验和无加密
  static const int securityModeChecksum = 0x1; // 有校验和无加密
  static const int securityModeEncrypt = 0x2; // 无校验和有加密
  static const int securityModeBoth = 0x3; // 有校验和有加密

  // 错误代码
  static const int errorSequence = 0x0; // 序列号错误
  static const int errorChecksum = 0x1; // 校验和错误
  static const int errorDecrypt = 0x2; // 解密错误
  static const int errorEncrypt = 0x3; // 加密错误
  static const int errorInitSecurity = 0x4; // 初始化安全错误
  static const int errorDhMalloc = 0x5; // DH内存分配错误
  static const int errorDhParam = 0x6; // DH参数错误
  static const int errorReadParam = 0x7; // 读取参数错误
  static const int errorMakePublic = 0x8; // 生成公钥错误
  static const int errorDataFormat = 0x9; // 数据格式错误
  static const int errorCalculateMd5 = 0xa; // 计算MD5错误
  static const int errorWifiScan = 0xb; // WiFi扫描错误
  static const int errorMsgState = 0xc; // 消息状态错误

  // 协议配置
  static const int maxDataLength = 20; // 单次传输最大数据长度
  static const int maxFrameLength = 256; // 最大帧长度
  static const int defaultTimeout = 10000; // 默认超时时间(毫秒)
  static const int maxRetryCount = 3; // 最大重试次数

  // DH密钥协商参数
  static const int dhKeyLength = 1024; // DH密钥长度(位)
  static const int aesKeyLength = 16; // AES密钥长度(字节)
  static const int ivLength = 16; // IV长度(字节)
}
