# ESP32 BluFi API 使用示例

本文档提供了ESP32 BluFi Flutter应用的详细API使用示例。

## 📋 目录

1. [基础设置](#基础设置)
2. [设备扫描和连接](#设备扫描和连接)
3. [WiFi配置](#wifi配置)
4. [状态监控](#状态监控)
5. [高级功能](#高级功能)
6. [错误处理](#错误处理)

## 🚀 基础设置

### 初始化服务

```dart
import 'package:esp32_blufi/services/blufi_service.dart';
import 'package:esp32_blufi/models/wifi_config.dart';

class BluFiManager {
  late BluFiService _blufiService;
  
  Future<void> initialize() async {
    _blufiService = BluFiService();
    
    // 初始化服务
    final success = await _blufiService.initialize();
    if (!success) {
      throw Exception('BluFi服务初始化失败');
    }
    
    // 监听连接状态
    _blufiService.connectionStateStream.listen((state) {
      print('连接状态: $state');
    });
    
    // 监听错误
    _blufiService.errorStream.listen((error) {
      print('错误: $error');
    });
  }
  
  void dispose() {
    _blufiService.dispose();
  }
}
```

### Provider设置

```dart
import 'package:provider/provider.dart';
import 'package:esp32_blufi/viewmodels/device_scan_viewmodel.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => DeviceScanViewModel()),
        ChangeNotifierProvider(create: (_) => DeviceConfigViewModel()),
        ChangeNotifierProvider(create: (_) => ConnectionViewModel()),
      ],
      child: MaterialApp(
        title: 'ESP32 BluFi',
        home: DeviceScanPage(),
      ),
    );
  }
}
```

## 🔍 设备扫描和连接

### 扫描BluFi设备

```dart
class DeviceScanExample {
  late DeviceScanViewModel _viewModel;
  
  Future<void> scanForDevices() async {
    _viewModel = DeviceScanViewModel();
    
    // 检查权限
    if (!_viewModel.hasPermissions) {
      await _viewModel.requestPermissions();
    }
    
    // 检查蓝牙状态
    if (!_viewModel.isBluetoothEnabled) {
      await _viewModel.enableBluetooth();
    }
    
    // 开始扫描
    await _viewModel.startScan();
    
    // 监听发现的设备
    _viewModel.addListener(() {
      final devices = _viewModel.discoveredDevices;
      print('发现 ${devices.length} 个设备');
      
      for (final device in devices) {
        print('设备: ${device.name}, RSSI: ${device.rssi}dBm');
      }
    });
  }
  
  Future<void> connectToDevice(BluFiDevice device) async {
    final success = await _viewModel.connectToDevice(device);
    if (success) {
      print('连接成功: ${device.name}');
      // 导航到配置页面
    } else {
      print('连接失败: ${device.name}');
    }
  }
}
```

### 自动连接最强信号设备

```dart
Future<BluFiDevice?> findBestDevice() async {
  await _viewModel.startScan();
  
  // 等待扫描完成
  await Future.delayed(const Duration(seconds: 5));
  await _viewModel.stopScan();
  
  final devices = _viewModel.discoveredDevices;
  if (devices.isEmpty) return null;
  
  // 选择信号最强的设备
  devices.sort((a, b) => b.rssi.compareTo(a.rssi));
  return devices.first;
}
```

## 📶 WiFi配置

### Station模式配置

```dart
class WiFiConfigExample {
  late DeviceConfigViewModel _viewModel;
  
  Future<void> configureStationMode() async {
    _viewModel = DeviceConfigViewModel();
    
    // 创建Station模式配置
    final config = WiFiConfig.station(
      ssid: 'MyHomeWiFi',
      password: 'MyPassword123',
    );
    
    // 验证配置
    if (!config.isValid) {
      throw Exception('WiFi配置无效');
    }
    
    // 发送配置
    final success = await _viewModel.configureWifi(config);
    if (success) {
      print('WiFi配置成功');
      
      // 等待连接
      await _waitForWiFiConnection();
    } else {
      print('WiFi配置失败');
    }
  }
  
  Future<void> _waitForWiFiConnection() async {
    const maxWaitTime = Duration(seconds: 30);
    const checkInterval = Duration(seconds: 2);
    
    final startTime = DateTime.now();
    
    while (DateTime.now().difference(startTime) < maxWaitTime) {
      final status = await _viewModel.getWiFiStatus();
      
      if (status?.isConnected == true) {
        print('WiFi连接成功: ${status!.ssid}');
        print('IP地址: ${status.ipAddress}');
        return;
      }
      
      await Future.delayed(checkInterval);
    }
    
    throw Exception('WiFi连接超时');
  }
}
```

### SoftAP模式配置

```dart
Future<void> configureSoftAPMode() async {
  final config = WiFiConfig.softAP(
    ssid: 'ESP32-HotSpot',
    password: 'ESP32Password',
    channel: 6,
    maxConnections: 4,
    authMode: WiFiAuthMode.wpa2Psk,
  );
  
  final success = await _viewModel.configureWifi(config);
  if (success) {
    print('SoftAP配置成功');
    print('热点名称: ${config.apSsid}');
    print('密码: ${config.apPassword}');
  }
}
```

### 混合模式配置

```dart
Future<void> configureStationSoftAPMode() async {
  final config = WiFiConfig.stationSoftAP(
    staSsid: 'HomeNetwork',
    staPassword: 'HomePassword',
    apSsid: 'ESP32-Bridge',
    apPassword: 'BridgePassword',
    apChannel: 11,
    apMaxConnections: 2,
  );
  
  final success = await _viewModel.configureWifi(config);
  if (success) {
    print('混合模式配置成功');
    print('连接到: ${config.staSsid}');
    print('提供热点: ${config.apSsid}');
  }
}
```

### 企业级WiFi配置

```dart
Future<void> configureEnterpriseWiFi() async {
  final config = WiFiConfig.enterprise(
    ssid: 'CorporateNetwork',
    username: '<EMAIL>',
    password: 'EmployeePassword',
  );
  
  // 可选：添加证书
  if (caCertData != null) {
    config = config.copyWith(caCert: caCertData);
  }
  
  final success = await _viewModel.configureWifi(config);
  if (success) {
    print('企业级WiFi配置成功');
  }
}
```

## 📊 状态监控

### 实时状态监控

```dart
class StatusMonitor {
  late ConnectionViewModel _viewModel;
  late StreamSubscription _statusSubscription;
  
  void startMonitoring() {
    _viewModel = ConnectionViewModel();
    
    // 监听连接状态变化
    _statusSubscription = _viewModel.connectionStateStream.listen((state) {
      switch (state) {
        case ConnectionState.disconnected:
          print('设备已断开');
          break;
        case ConnectionState.connecting:
          print('正在连接...');
          break;
        case ConnectionState.connected:
          print('设备已连接');
          _startPeriodicStatusCheck();
          break;
        case ConnectionState.configuring:
          print('正在配置WiFi...');
          break;
      }
    });
  }
  
  void _startPeriodicStatusCheck() {
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (!_viewModel.isConnected) {
        timer.cancel();
        return;
      }
      
      // 刷新WiFi状态
      await _viewModel.refreshWifiStatus();
      
      // 获取设备版本（如果还没有）
      if (_viewModel.deviceVersion == null) {
        await _viewModel.queryDeviceVersion();
      }
    });
  }
  
  void dispose() {
    _statusSubscription.cancel();
  }
}
```

### 获取详细状态信息

```dart
Future<void> getDetailedStatus() async {
  final viewModel = ConnectionViewModel();
  
  // 获取连接状态
  print('连接状态: ${viewModel.connectionStatusDescription}');
  
  // 获取WiFi状态
  final wifiStatus = viewModel.wifiStatus;
  if (wifiStatus != null) {
    print('WiFi SSID: ${wifiStatus.ssid}');
    print('WiFi状态: ${wifiStatus.isConnected ? "已连接" : "未连接"}');
    print('IP地址: ${wifiStatus.ipAddress}');
    print('信号强度: ${wifiStatus.rssi}dBm');
  }
  
  // 获取设备版本
  final version = viewModel.deviceVersion;
  if (version != null) {
    print('设备版本: $version');
  }
  
  // 获取错误信息
  final error = viewModel.errorMessage;
  if (error != null) {
    print('错误: $error');
  }
}
```

## 🔧 高级功能

### 自定义数据传输

```dart
Future<void> sendCustomData() async {
  final customData = [0x01, 0x02, 0x03, 0x04]; // 自定义协议数据
  
  final success = await _blufiService.sendCustomData(customData);
  if (success) {
    print('自定义数据发送成功');
  }
}
```

### WiFi网络扫描

```dart
Future<List<String>> scanWiFiNetworks() async {
  final success = await _blufiService.scanWiFiNetworks();
  if (!success) {
    throw Exception('WiFi扫描失败');
  }
  
  // 等待扫描结果
  final completer = Completer<List<String>>();
  late StreamSubscription subscription;
  
  subscription = _blufiService.wifiListStream.listen((networks) {
    subscription.cancel();
    completer.complete(networks);
  });
  
  // 超时处理
  Timer(const Duration(seconds: 10), () {
    if (!completer.isCompleted) {
      subscription.cancel();
      completer.completeError('WiFi扫描超时');
    }
  });
  
  return completer.future;
}
```

### 设备版本查询

```dart
Future<String> getDeviceVersion() async {
  final version = await _blufiService.queryDeviceVersion();
  if (version == null) {
    throw Exception('无法获取设备版本');
  }
  
  print('设备版本: $version');
  return version;
}
```

## ⚠️ 错误处理

### 全局错误处理

```dart
class ErrorHandler {
  static void setupGlobalErrorHandling(BluFiService blufiService) {
    // 监听BluFi错误
    blufiService.errorStream.listen((error) {
      _handleBluFiError(error);
    });
    
    // 监听连接状态错误
    blufiService.connectionStateStream.listen((state) {
      if (state == ConnectionState.error) {
        _handleConnectionError();
      }
    });
  }
  
  static void _handleBluFiError(String error) {
    print('BluFi错误: $error');
    
    // 根据错误类型采取不同处理策略
    if (error.contains('连接超时')) {
      _handleConnectionTimeout();
    } else if (error.contains('权限')) {
      _handlePermissionError();
    } else if (error.contains('蓝牙')) {
      _handleBluetoothError();
    } else {
      _handleGenericError(error);
    }
  }
  
  static void _handleConnectionTimeout() {
    // 重试连接逻辑
    print('连接超时，准备重试...');
  }
  
  static void _handlePermissionError() {
    // 权限请求逻辑
    print('权限不足，请检查应用权限设置');
  }
  
  static void _handleBluetoothError() {
    // 蓝牙问题处理
    print('蓝牙问题，请检查蓝牙设置');
  }
  
  static void _handleGenericError(String error) {
    // 通用错误处理
    print('发生错误: $error');
  }
  
  static void _handleConnectionError() {
    print('连接错误，请检查设备状态');
  }
}
```

### 重试机制

```dart
class RetryHelper {
  static Future<T> withRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        print('操作失败，第 $attempts 次重试 (最多 $maxRetries 次)');
        await Future.delayed(delay);
      }
    }
    
    throw Exception('重试次数已用完');
  }
}

// 使用示例
Future<void> connectWithRetry(BluFiDevice device) async {
  await RetryHelper.withRetry(
    () => _viewModel.connectToDevice(device),
    maxRetries: 3,
    delay: const Duration(seconds: 2),
  );
}
```

## 📝 完整示例

```dart
class CompleteBluFiExample {
  late BluFiService _blufiService;
  late DeviceScanViewModel _scanViewModel;
  late DeviceConfigViewModel _configViewModel;
  
  Future<void> runCompleteExample() async {
    try {
      // 1. 初始化
      await _initialize();
      
      // 2. 扫描设备
      final device = await _scanAndSelectDevice();
      
      // 3. 连接设备
      await _connectToDevice(device);
      
      // 4. 配置WiFi
      await _configureWiFi();
      
      // 5. 监控状态
      await _monitorStatus();
      
      print('BluFi配置流程完成！');
      
    } catch (e) {
      print('配置过程中发生错误: $e');
    } finally {
      _cleanup();
    }
  }
  
  Future<void> _initialize() async {
    _blufiService = BluFiService();
    _scanViewModel = DeviceScanViewModel();
    _configViewModel = DeviceConfigViewModel();
    
    await _blufiService.initialize();
    print('服务初始化完成');
  }
  
  Future<BluFiDevice> _scanAndSelectDevice() async {
    await _scanViewModel.startScan();
    await Future.delayed(const Duration(seconds: 5));
    await _scanViewModel.stopScan();
    
    final devices = _scanViewModel.discoveredDevices;
    if (devices.isEmpty) {
      throw Exception('未发现BluFi设备');
    }
    
    // 选择信号最强的设备
    devices.sort((a, b) => b.rssi.compareTo(a.rssi));
    print('选择设备: ${devices.first.name}');
    return devices.first;
  }
  
  Future<void> _connectToDevice(BluFiDevice device) async {
    final success = await _scanViewModel.connectToDevice(device);
    if (!success) {
      throw Exception('设备连接失败');
    }
    print('设备连接成功');
  }
  
  Future<void> _configureWiFi() async {
    final config = WiFiConfig.station(
      ssid: 'MyWiFi',
      password: 'MyPassword',
    );
    
    final success = await _configViewModel.configureWifi(config);
    if (!success) {
      throw Exception('WiFi配置失败');
    }
    print('WiFi配置成功');
  }
  
  Future<void> _monitorStatus() async {
    // 等待WiFi连接
    for (int i = 0; i < 15; i++) {
      await Future.delayed(const Duration(seconds: 2));
      
      final status = await _configViewModel.getWiFiStatus();
      if (status?.isConnected == true) {
        print('WiFi连接成功: ${status!.ipAddress}');
        return;
      }
    }
    
    throw Exception('WiFi连接超时');
  }
  
  void _cleanup() {
    _blufiService.dispose();
    _scanViewModel.dispose();
    _configViewModel.dispose();
  }
}
```

这些示例展示了ESP32 BluFi Flutter应用的完整API使用方法，涵盖了从基础设置到高级功能的所有场景。
