import 'package:logger/logger.dart';
import '../services/blufi_service.dart';
import '../models/connection_state.dart';

/// 连接调试助手
/// 用于诊断和解决连接状态同步问题
class ConnectionDebugHelper {
  static final Logger _logger = Logger();

  /// 诊断连接状态
  static Map<String, dynamic> diagnoseConnectionState(BluFiService blufiService) {
    final diagnosis = <String, dynamic>{};
    
    try {
      // 获取服务状态
      final serviceState = blufiService.state;
      diagnosis['serviceState'] = serviceState.toString();
      
      // 获取连接的设备
      final connectedDevice = blufiService.connectedDevice;
      diagnosis['hasConnectedDevice'] = connectedDevice != null;
      diagnosis['deviceName'] = connectedDevice?.name ?? 'None';
      diagnosis['deviceId'] = connectedDevice?.deviceId ?? 'None';
      
      // 检查状态一致性
      final isReady = serviceState == BluFiServiceState.ready;
      final isConnecting = serviceState == BluFiServiceState.connecting;
      final isNegotiating = serviceState == BluFiServiceState.negotiating;
      
      diagnosis['isReady'] = isReady;
      diagnosis['isConnecting'] = isConnecting;
      diagnosis['isNegotiating'] = isNegotiating;
      
      // 状态建议
      final suggestions = <String>[];
      
      if (isConnecting && connectedDevice != null) {
        suggestions.add('设备已连接但服务状态显示连接中，可能是密钥协商问题');
      }
      
      if (isNegotiating) {
        suggestions.add('正在进行密钥协商，如果长时间停留在此状态，可能需要重新连接');
      }
      
      if (!isReady && connectedDevice != null) {
        suggestions.add('设备已连接但服务未就绪，建议检查BluFi协议状态');
      }
      
      if (connectedDevice == null) {
        suggestions.add('没有连接的设备，需要先连接设备');
      }
      
      diagnosis['suggestions'] = suggestions;
      diagnosis['timestamp'] = DateTime.now().toIso8601String();
      
      _logger.i('Connection diagnosis completed: $diagnosis');
      
    } catch (e) {
      _logger.e('Error during connection diagnosis: $e');
      diagnosis['error'] = e.toString();
    }
    
    return diagnosis;
  }

  /// 尝试修复连接状态
  static Future<bool> attemptStateFix(BluFiService blufiService) async {
    try {
      _logger.i('Attempting to fix connection state');
      
      final diagnosis = diagnoseConnectionState(blufiService);
      final serviceState = blufiService.state;
      final connectedDevice = blufiService.connectedDevice;
      
      // 如果有连接的设备但服务状态不正确，尝试重置
      if (connectedDevice != null && serviceState != BluFiServiceState.ready) {
        _logger.i('Device connected but service not ready, attempting reset');
        
        // 断开并重新连接
        await blufiService.disconnect();
        await Future.delayed(const Duration(seconds: 2));
        
        final reconnected = await blufiService.connectToDevice(connectedDevice);
        if (reconnected) {
          _logger.i('Reconnection successful');
          return true;
        } else {
          _logger.w('Reconnection failed');
          return false;
        }
      }
      
      // 如果服务状态是negotiating但时间过长，强制重置
      if (serviceState == BluFiServiceState.negotiating) {
        _logger.i('Service stuck in negotiating state, forcing reset');
        await blufiService.disconnect();
        await Future.delayed(const Duration(seconds: 1));
        
        if (connectedDevice != null) {
          return await blufiService.connectToDevice(connectedDevice);
        }
      }
      
      return false;
    } catch (e) {
      _logger.e('Error attempting state fix: $e');
      return false;
    }
  }

  /// 获取连接状态报告
  static String getConnectionReport(BluFiService blufiService) {
    final diagnosis = diagnoseConnectionState(blufiService);
    final buffer = StringBuffer();
    
    buffer.writeln('=== 连接状态报告 ===');
    buffer.writeln('时间: ${diagnosis['timestamp']}');
    buffer.writeln('服务状态: ${diagnosis['serviceState']}');
    buffer.writeln('设备连接: ${diagnosis['hasConnectedDevice'] ? '是' : '否'}');
    
    if (diagnosis['hasConnectedDevice']) {
      buffer.writeln('设备名称: ${diagnosis['deviceName']}');
      buffer.writeln('设备ID: ${diagnosis['deviceId']}');
    }
    
    buffer.writeln('服务就绪: ${diagnosis['isReady'] ? '是' : '否'}');
    buffer.writeln('正在连接: ${diagnosis['isConnecting'] ? '是' : '否'}');
    buffer.writeln('密钥协商: ${diagnosis['isNegotiating'] ? '是' : '否'}');
    
    final suggestions = diagnosis['suggestions'] as List<String>?;
    if (suggestions != null && suggestions.isNotEmpty) {
      buffer.writeln('\n建议:');
      for (int i = 0; i < suggestions.length; i++) {
        buffer.writeln('${i + 1}. ${suggestions[i]}');
      }
    }
    
    if (diagnosis.containsKey('error')) {
      buffer.writeln('\n错误: ${diagnosis['error']}');
    }
    
    return buffer.toString();
  }

  /// 记录详细的连接状态
  static void logDetailedConnectionState(BluFiService blufiService) {
    final report = getConnectionReport(blufiService);
    _logger.i('Detailed connection state:\n$report');
  }

  /// 检查状态是否异常
  static bool isStateAbnormal(BluFiService blufiService) {
    final serviceState = blufiService.state;
    final connectedDevice = blufiService.connectedDevice;
    
    // 有设备连接但服务不就绪
    if (connectedDevice != null && serviceState != BluFiServiceState.ready) {
      return true;
    }
    
    // 长时间处于连接或协商状态
    if (serviceState == BluFiServiceState.connecting || 
        serviceState == BluFiServiceState.negotiating) {
      return true;
    }
    
    return false;
  }

  /// 获取状态修复建议
  static List<String> getFixSuggestions(BluFiService blufiService) {
    final suggestions = <String>[];
    final serviceState = blufiService.state;
    final connectedDevice = blufiService.connectedDevice;
    
    if (connectedDevice == null) {
      suggestions.add('重新扫描并连接设备');
    } else if (serviceState == BluFiServiceState.connecting) {
      suggestions.add('等待连接完成或重新连接');
    } else if (serviceState == BluFiServiceState.negotiating) {
      suggestions.add('等待密钥协商完成或重新连接');
    } else if (serviceState == BluFiServiceState.error) {
      suggestions.add('检查错误信息并重新连接');
    } else if (serviceState != BluFiServiceState.ready) {
      suggestions.add('强制刷新状态或重新连接');
    }
    
    return suggestions;
  }
}
