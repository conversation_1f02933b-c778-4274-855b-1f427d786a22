import 'package:flutter/material.dart';

/// BluFi消息类型
enum BluFiMessageType {
  info,        // 普通信息
  notification, // 通知消息
  error,       // 错误消息
  success,     // 成功消息
}

/// BluFi消息
class BluFiMessage {
  final String text;
  final BluFiMessageType type;
  final DateTime timestamp;

  BluFiMessage({
    required this.text,
    this.type = BluFiMessageType.info,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Color get textColor {
    switch (type) {
      case BluFiMessageType.info:
        return Colors.black;
      case BluFiMessageType.notification:
        return Colors.red;
      case BluFiMessageType.error:
        return Colors.red;
      case BluFiMessageType.success:
        return Colors.green;
    }
  }

  IconData get icon {
    switch (type) {
      case BluFiMessageType.info:
        return Icons.info_outline;
      case BluFiMessageType.notification:
        return Icons.notifications;
      case BluFiMessageType.error:
        return Icons.error_outline;
      case BluFiMessageType.success:
        return Icons.check_circle_outline;
    }
  }
}

/// BluFi消息列表组件
/// 模仿Android版本的消息显示功能
class BluFiMessageList extends StatefulWidget {
  final List<BluFiMessage> messages;
  final VoidCallback? onClear;

  const BluFiMessageList({
    super.key,
    required this.messages,
    this.onClear,
  });

  @override
  State<BluFiMessageList> createState() => _BluFiMessageListState();
}

class _BluFiMessageListState extends State<BluFiMessageList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void didUpdateWidget(BluFiMessageList oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 当有新消息时自动滚动到底部
    if (widget.messages.length > oldWidget.messages.length) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.message,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'BluFi Messages',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${widget.messages.length} messages',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              if (widget.onClear != null && widget.messages.isNotEmpty) ...[
                const SizedBox(width: 8),
                IconButton(
                  onPressed: widget.onClear,
                  icon: const Icon(Icons.clear_all),
                  tooltip: 'Clear all messages',
                  iconSize: 20,
                ),
              ],
            ],
          ),
        ),
        
        // 消息列表
        Expanded(
          child: widget.messages.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: widget.messages.length,
                  itemBuilder: (context, index) {
                    return _buildMessageItem(widget.messages[index]);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.message_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No messages yet',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'BluFi communication messages will appear here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageItem(BluFiMessage message) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 2),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 消息图标
            Icon(
              message.icon,
              color: message.textColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            
            // 消息内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 消息文本
                  Text(
                    message.text,
                    style: TextStyle(
                      color: message.textColor,
                      fontSize: 14,
                    ),
                  ),
                  
                  // 时间戳
                  const SizedBox(height: 4),
                  Text(
                    _formatTimestamp(message.timestamp),
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.hour.toString().padLeft(2, '0')}:'
             '${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
}

/// BluFi消息管理器
class BluFiMessageManager extends ChangeNotifier {
  final List<BluFiMessage> _messages = [];

  List<BluFiMessage> get messages => List.unmodifiable(_messages);

  void addMessage(String text, {BluFiMessageType type = BluFiMessageType.info}) {
    _messages.add(BluFiMessage(text: text, type: type));
    notifyListeners();
  }

  void addInfoMessage(String text) {
    addMessage(text, type: BluFiMessageType.info);
  }

  void addNotificationMessage(String text) {
    addMessage(text, type: BluFiMessageType.notification);
  }

  void addErrorMessage(String text) {
    addMessage(text, type: BluFiMessageType.error);
  }

  void addSuccessMessage(String text) {
    addMessage(text, type: BluFiMessageType.success);
  }

  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  void removeMessage(int index) {
    if (index >= 0 && index < _messages.length) {
      _messages.removeAt(index);
      notifyListeners();
    }
  }
}
