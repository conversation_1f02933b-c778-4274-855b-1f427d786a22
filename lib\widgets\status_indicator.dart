import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// 状态指示器组件
/// 显示各种状态的可视化指示器
class StatusIndicator extends StatelessWidget {
  final String label;
  final String value;
  final StatusType type;
  final IconData? icon;
  final VoidCallback? onTap;

  const StatusIndicator({
    Key? key,
    required this.label,
    required this.value,
    this.type = StatusType.info,
    this.icon,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: _getStatusColor(),
                  size: 24,
                ),
                const SizedBox(width: AppConstants.defaultPadding),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: _getStatusColor(),
                      ),
                    ),
                  ],
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey[400],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (type) {
      case StatusType.success:
        return const Color(AppColors.successColorValue);
      case StatusType.warning:
        return const Color(AppColors.warningColorValue);
      case StatusType.error:
        return const Color(AppColors.errorColorValue);
      case StatusType.info:
      default:
        return const Color(AppColors.infoColorValue);
    }
  }
}

/// 连接质量指示器
class ConnectionQualityIndicator extends StatelessWidget {
  final int quality; // 0-100
  final String? label;

  const ConnectionQualityIndicator({
    Key? key,
    required this.quality,
    this.label,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
        ],
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildQualityBar(0, 20),
            const SizedBox(width: 2),
            _buildQualityBar(20, 40),
            const SizedBox(width: 2),
            _buildQualityBar(40, 60),
            const SizedBox(width: 2),
            _buildQualityBar(60, 80),
            const SizedBox(width: 2),
            _buildQualityBar(80, 100),
            const SizedBox(width: 8),
            Text(
              '$quality%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: _getQualityColor(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQualityBar(int minThreshold, int maxThreshold) {
    final isActive = quality > minThreshold;
    return Container(
      width: 8,
      height: 20,
      decoration: BoxDecoration(
        color: isActive ? _getQualityColor() : Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Color _getQualityColor() {
    if (quality >= 80) return Colors.green;
    if (quality >= 60) return Colors.lightGreen;
    if (quality >= 40) return Colors.orange;
    if (quality >= 20) return Colors.deepOrange;
    return Colors.red;
  }
}

/// 加载指示器
class LoadingIndicator extends StatelessWidget {
  final String message;
  final bool showProgress;

  const LoadingIndicator({
    Key? key,
    required this.message,
    this.showProgress = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showProgress) ...[
              const CircularProgressIndicator(),
              const SizedBox(height: AppConstants.defaultPadding),
            ],
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// 错误指示器
class ErrorIndicator extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final String? retryLabel;

  const ErrorIndicator({
    Key? key,
    required this.message,
    this.onRetry,
    this.retryLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: AppConstants.largePadding),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryLabel ?? '重试'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 信息卡片
class InfoCard extends StatelessWidget {
  final String title;
  final Map<String, String> info;
  final IconData? icon;
  final Color? color;

  const InfoCard({
    Key? key,
    required this.title,
    required this.info,
    this.icon,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    color: color ?? Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                ],
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...info.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 100,
                    child: Text(
                      entry.key,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Text(
                      entry.value,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}

/// 状态类型枚举
enum StatusType {
  success,
  warning,
  error,
  info,
}
