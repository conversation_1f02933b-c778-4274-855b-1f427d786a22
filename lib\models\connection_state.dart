import '../utils/blufi_constants.dart';

/// WiFi连接状态模型
class WiFiConnectionState {
  /// WiFi操作模式
  final int opMode;

  /// Station连接状态
  final int staState;

  /// SoftAP连接的设备数量
  final int softApConnCount;

  /// Station模式下连接的SSID
  final String? staSsid;

  /// Station模式下连接的BSSID
  final String? staBssid;

  /// Station模式下的IP地址
  final String? staIp;

  /// SoftAP模式下的IP地址
  final String? softApIp;

  /// 信号强度（RSSI）
  final int? rssi;

  /// 连接结束原因（如果已断开）
  final int? disconnectReason;

  /// 最大重连时间（秒）
  final int? maxReconnectTime;

  /// 最后更新时间
  final DateTime updatedAt;

  const WiFiConnectionState({
    required this.opMode,
    required this.staState,
    required this.softApConnCount,
    this.staSsid,
    this.staBssid,
    this.staIp,
    this.softApIp,
    this.rssi,
    this.disconnectReason,
    this.maxReconnectTime,
    required this.updatedAt,
  });

  /// 创建初始状态
  factory WiFiConnectionState.initial() {
    return WiFiConnectionState(
      opMode: BluFiConstants.wifiModeNull,
      staState: BluFiConstants.wifiStateDisconnected,
      softApConnCount: 0,
      updatedAt: DateTime.now(),
    );
  }

  /// 获取操作模式描述
  String get opModeDescription {
    switch (opMode) {
      case BluFiConstants.wifiModeSta:
        return 'Station模式';
      case BluFiConstants.wifiModeAp:
        return 'SoftAP模式';
      case BluFiConstants.wifiModeApSta:
        return 'Station+SoftAP模式';
      default:
        return '未知模式';
    }
  }

  /// 获取Station连接状态描述
  String get staStateDescription {
    switch (staState) {
      case BluFiConstants.wifiStateConnectedWithIp:
        return '已连接并获得IP';
      case BluFiConstants.wifiStateDisconnected:
        return '已断开';
      case BluFiConstants.wifiStateConnecting:
        return '连接中';
      case BluFiConstants.wifiStateConnectedNoIp:
        return '已连接但无IP';
      default:
        return '未知状态';
    }
  }

  /// 检查Station是否已连接
  bool get isStaConnected {
    return staState == BluFiConstants.wifiStateConnectedWithIp;
  }

  /// 检查SoftAP是否有设备连接
  bool get hasSoftApConnections {
    return softApConnCount > 0;
  }

  /// 获取信号强度描述
  String get rssiDescription {
    if (rssi == null) return '未知';
    if (rssi! >= -50) return '优秀';
    if (rssi! >= -60) return '良好';
    if (rssi! >= -70) return '一般';
    if (rssi! >= -80) return '较差';
    return '很差';
  }

  /// 获取断开连接原因描述
  String? get disconnectReasonDescription {
    if (disconnectReason == null) return null;

    // 常见的WiFi断开原因代码
    switch (disconnectReason) {
      case 1:
        return '未认证';
      case 2:
        return '无可用AP';
      case 3:
        return '认证超时';
      case 4:
        return '关联超时';
      case 5:
        return '关联失败';
      case 6:
        return '内部错误';
      case 7:
        return '认证失败';
      case 8:
        return 'AP不允许连接';
      case 9:
        return '密码错误';
      case 10:
        return '无法找到SSID';
      case 200:
        return '用户断开';
      case 201:
        return '低RSSI';
      case 202:
        return '未找到信道';
      case 203:
        return '方法不支持';
      case 204:
        return '无法连接';
      case 205:
        return '连接超时';
      default:
        return '错误代码: $disconnectReason';
    }
  }

  /// 创建更新后的状态
  WiFiConnectionState copyWith({
    int? opMode,
    int? staState,
    int? softApConnCount,
    String? staSsid,
    String? staBssid,
    String? staIp,
    String? softApIp,
    int? rssi,
    int? disconnectReason,
    int? maxReconnectTime,
  }) {
    return WiFiConnectionState(
      opMode: opMode ?? this.opMode,
      staState: staState ?? this.staState,
      softApConnCount: softApConnCount ?? this.softApConnCount,
      staSsid: staSsid ?? this.staSsid,
      staBssid: staBssid ?? this.staBssid,
      staIp: staIp ?? this.staIp,
      softApIp: softApIp ?? this.softApIp,
      rssi: rssi ?? this.rssi,
      disconnectReason: disconnectReason ?? this.disconnectReason,
      maxReconnectTime: maxReconnectTime ?? this.maxReconnectTime,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'WiFiConnectionState{opMode: $opMode, staState: $staState, '
        'softApConnCount: $softApConnCount, staSsid: $staSsid, '
        'staBssid: $staBssid, staIp: $staIp, softApIp: $softApIp, '
        'rssi: $rssi, disconnectReason: $disconnectReason, '
        'maxReconnectTime: $maxReconnectTime, updatedAt: $updatedAt}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'opMode': opMode,
      'staState': staState,
      'softApConnCount': softApConnCount,
      'staSsid': staSsid,
      'staBssid': staBssid,
      'staIp': staIp,
      'softApIp': softApIp,
      'rssi': rssi,
      'disconnectReason': disconnectReason,
      'maxReconnectTime': maxReconnectTime,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 从JSON创建
  factory WiFiConnectionState.fromJson(Map<String, dynamic> json) {
    return WiFiConnectionState(
      opMode: json['opMode'] ?? BluFiConstants.wifiModeNull,
      staState: json['staState'] ?? BluFiConstants.wifiStateDisconnected,
      softApConnCount: json['softApConnCount'] ?? 0,
      staSsid: json['staSsid'],
      staBssid: json['staBssid'],
      staIp: json['staIp'],
      softApIp: json['softApIp'],
      rssi: json['rssi'],
      disconnectReason: json['disconnectReason'],
      maxReconnectTime: json['maxReconnectTime'],
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }
}
