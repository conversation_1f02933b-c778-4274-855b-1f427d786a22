import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'package:provider/provider.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../services/blufi_service_copy.dart';
import 'package:logger/logger.dart'; // 核心日志库

class BlufiTestPage extends StatefulWidget {
  const BlufiTestPage({super.key});

  @override
  State<BlufiTestPage> createState() => _BlufiTestPageState();
}

class _BlufiTestPageState extends State<BlufiTestPage> {
  late BlufiService _blufiService;
  final Logger _logger = Logger(); // 创建日志记录器实例
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _blufiService = Provider.of<BlufiService>(context, listen: false);
    });
  }

  Future<void> _test() async {
    const deviceName = 'PETCARE';
    BluetoothDevice? targetDevice;
    var subscription = FlutterBluePlus.scanResults.listen((results) {
      for (ScanResult r in results) {
        targetDevice = r.device;
        // 找到设备后立即停止扫描
        FlutterBluePlus.stopScan();
      }
    });

    // 开始扫描
    await FlutterBluePlus.startScan(
        withNames: [deviceName], timeout: const Duration(seconds: 5));

    // 等待一小段时间确保扫描结果被处理
    await Future.delayed(const Duration(seconds: 5));
    await subscription.cancel();
    if (targetDevice == null) {
      return;
    } else {
      targetDevice!.connect(timeout: const Duration(seconds: 5));
      await Future.delayed(const Duration(seconds: 5));
      // 接通服务
      _blufiService.setupBlufiDataListener(targetDevice!);
    }

    List<int> sendData = []; // 自定义数据帧内容，可以根据需要修改
    // 字符串转换为字节数组
    String dataString = 'A';
    String hexString = dataString.codeUnits
        .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join('');
    for (int i = 0; i < hexString.length; i += 2) {
      sendData.add(int.parse(hexString.substring(i, i + 2), radix: 16));
    }
    final pdata = _blufiService.createCustomDataFrame(sendData); // 自定义数据帧
    await _blufiService.writeBlufiData(targetDevice!, pdata);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BluFi测试'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: _test,
          child: const Text('测试'),
        ),
      ),
    );
  }
}
