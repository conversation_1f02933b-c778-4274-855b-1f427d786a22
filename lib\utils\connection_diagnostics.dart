import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:logger/logger.dart';
import '../services/blufi_service.dart';
import '../models/blufi_device.dart';

/// 连接诊断工具
/// 用于诊断和修复蓝牙连接问题
class ConnectionDiagnostics {
  static final Logger _logger = Logger();

  /// 诊断连接问题
  static Future<DiagnosticResult> diagnoseConnection(
    BluFiDevice device,
    BluFiService blufiService,
  ) async {
    final result = DiagnosticResult();

    try {
      _logger.i('Starting connection diagnostics for device: ${device.name}');

      // 1. 检查蓝牙适配器状态
      result.bluetoothAdapterState = await _checkBluetoothAdapter();
      if (result.bluetoothAdapterState != fbp.BluetoothAdapterState.on) {
        result.issues.add('蓝牙适配器未开启');
        result.recommendations.add('请开启蓝牙');
        return result;
      }

      // 2. 检查设备连接状态
      result.deviceConnectionState = await _checkDeviceConnection(device);

      // 3. 检查GATT服务
      if (result.deviceConnectionState ==
          fbp.BluetoothConnectionState.connected) {
        result.gattServices = await _checkGattServices(device.bluetoothDevice);
        result.hasRequiredServices =
            _validateRequiredServices(result.gattServices);

        if (!result.hasRequiredServices) {
          result.issues.add('设备缺少必需的BluFi服务');
          result.recommendations.add('确认设备支持BluFi协议');
        }
      }

      // 4. 检查服务状态
      result.blufiServiceState = blufiService.state;

      // 5. 检查连接超时
      result.connectionTimeout = await _checkConnectionTimeout(device);
      if (result.connectionTimeout) {
        result.issues.add('连接超时');
        result.recommendations.add('尝试重新连接或检查设备距离');
      }

      // 6. 生成诊断报告
      _generateDiagnosticReport(result);
    } catch (e) {
      _logger.e('Diagnostic error: $e');
      result.issues.add('诊断过程中发生错误: $e');
    }

    return result;
  }

  /// 检查蓝牙适配器状态
  static Future<fbp.BluetoothAdapterState> _checkBluetoothAdapter() async {
    try {
      return await fbp.FlutterBluePlus.adapterState.first;
    } catch (e) {
      _logger.e('Failed to check adapter state: $e');
      return fbp.BluetoothAdapterState.unknown;
    }
  }

  /// 检查设备连接状态
  static Future<fbp.BluetoothConnectionState> _checkDeviceConnection(
      BluFiDevice device) async {
    try {
      final connectionState =
          await device.bluetoothDevice.connectionState.first;
      switch (connectionState) {
        case fbp.BluetoothConnectionState.connected:
          return fbp.BluetoothConnectionState.connected;
        case fbp.BluetoothConnectionState.disconnected:
          return fbp.BluetoothConnectionState.disconnected;
        default:
          // 处理其他状态（如connecting, disconnecting等已弃用状态）
          return fbp.BluetoothConnectionState.disconnected;
      }
    } catch (e) {
      _logger.e('Failed to check device connection: $e');
      return fbp.BluetoothConnectionState.disconnected;
    }
  }

  /// 检查GATT服务
  static Future<List<fbp.BluetoothService>> _checkGattServices(
      fbp.BluetoothDevice device) async {
    try {
      return await device.discoverServices();
    } catch (e) {
      _logger.e('Failed to discover services: $e');
      return [];
    }
  }

  /// 验证必需的服务
  static bool _validateRequiredServices(List<fbp.BluetoothService> services) {
    bool hasBlufiService = false;
    bool hasWriteCharacteristic = false;
    bool hasNotifyCharacteristic = false;

    for (final service in services) {
      final serviceUuid = service.uuid.toString().toUpperCase();
      if (serviceUuid.contains('FFFF')) {
        hasBlufiService = true;

        for (final characteristic in service.characteristics) {
          final charUuid = characteristic.uuid.toString().toUpperCase();
          if (charUuid.contains('FF01')) {
            hasWriteCharacteristic = true;
          } else if (charUuid.contains('FF02')) {
            hasNotifyCharacteristic = true;
          }
        }
      }
    }

    return hasBlufiService && hasWriteCharacteristic && hasNotifyCharacteristic;
  }

  /// 检查连接超时
  static Future<bool> _checkConnectionTimeout(BluFiDevice device) async {
    const timeout = Duration(seconds: 20);

    try {
      await device.bluetoothDevice.connectionState
          .where((state) => state == fbp.BluetoothConnectionState.connected)
          .timeout(timeout)
          .first;

      return false; // 没有超时
    } on TimeoutException {
      return true; // 超时了
    } catch (e) {
      _logger.e('Connection timeout check error: $e');
      return true;
    }
  }

  /// 生成诊断报告
  static void _generateDiagnosticReport(DiagnosticResult result) {
    if (result.issues.isEmpty) {
      result.summary = '连接诊断正常';
      result.severity = DiagnosticSeverity.info;
    } else if (result.issues.length == 1) {
      result.summary = '发现1个问题';
      result.severity = DiagnosticSeverity.warning;
    } else {
      result.summary = '发现${result.issues.length}个问题';
      result.severity = DiagnosticSeverity.error;
    }
  }

  /// 尝试修复连接问题
  static Future<bool> attemptConnectionFix(
    BluFiDevice device,
    BluFiService blufiService,
    DiagnosticResult diagnosticResult,
  ) async {
    try {
      _logger.i('Attempting to fix connection issues');

      // 1. 如果设备已连接但服务状态异常，尝试重置服务
      if (diagnosticResult.deviceConnectionState ==
              fbp.BluetoothConnectionState.connected &&
          diagnosticResult.blufiServiceState != BluFiServiceState.ready) {
        _logger.i('Resetting BluFi service');
        await blufiService.disconnect();
        await Future.delayed(const Duration(seconds: 2));
        return await blufiService.connectToDevice(device);
      }

      // 2. 如果连接超时，尝试重新连接
      if (diagnosticResult.connectionTimeout) {
        _logger.i('Retrying connection due to timeout');
        await blufiService.disconnect();
        await Future.delayed(const Duration(seconds: 3));
        return await blufiService.connectToDevice(device);
      }

      // 3. 如果缺少必需服务，无法修复
      if (!diagnosticResult.hasRequiredServices) {
        _logger.e('Cannot fix: device lacks required services');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Failed to fix connection: $e');
      return false;
    }
  }
}

/// 诊断结果
class DiagnosticResult {
  fbp.BluetoothAdapterState bluetoothAdapterState =
      fbp.BluetoothAdapterState.unknown;
  fbp.BluetoothConnectionState deviceConnectionState =
      fbp.BluetoothConnectionState.disconnected;
  BluFiServiceState blufiServiceState = BluFiServiceState.idle;
  List<fbp.BluetoothService> gattServices = [];
  bool hasRequiredServices = false;
  bool connectionTimeout = false;

  List<String> issues = [];
  List<String> recommendations = [];
  String summary = '';
  DiagnosticSeverity severity = DiagnosticSeverity.info;
}

/// 诊断严重程度
enum DiagnosticSeverity {
  info,
  warning,
  error,
}
