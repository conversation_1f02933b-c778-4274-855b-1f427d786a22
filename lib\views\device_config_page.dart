import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/device_config_viewmodel.dart';
import '../widgets/wifi_config_form.dart';
import '../widgets/status_indicator.dart';
import '../widgets/security_settings_widget.dart';
import '../widgets/blufi_message_list.dart';
import '../utils/constants.dart';
import 'connection_status_page.dart';
import 'configure_options_page.dart';

/// 设备配置页面
/// 模仿Android版本的BlufiActivity，提供完整的BluFi功能
class DeviceConfigPage extends StatefulWidget {
  const DeviceConfigPage({super.key});

  @override
  State<DeviceConfigPage> createState() => _DeviceConfigPageState();
}

class _DeviceConfigPageState extends State<DeviceConfigPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DeviceConfigViewModel>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WiFi配置'),
        actions: [
          Consumer<DeviceConfigViewModel>(
            builder: (context, viewModel, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 安全设置按钮
                  IconButton(
                    onPressed: () => _showSecuritySettings(viewModel),
                    icon: const Icon(Icons.security),
                    tooltip: '安全设置',
                  ),
                  // 调试按钮
                  IconButton(
                    onPressed: () => _debugConnection(viewModel),
                    icon: const Icon(Icons.bug_report),
                    tooltip: '调试连接',
                  ),
                  // WiFi扫描按钮
                  IconButton(
                    onPressed: viewModel.isReady
                        ? () => _getWifiList(viewModel)
                        : null,
                    icon: viewModel.isLoadingWifiList
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.wifi_find),
                    tooltip: '扫描WiFi',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<DeviceConfigViewModel>(
        builder: (context, viewModel, child) {
          return Column(
            children: [
              _buildStatusBar(viewModel),
              Expanded(
                child: _buildContent(viewModel),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar: Consumer<DeviceConfigViewModel>(
        builder: (context, viewModel, child) {
          return _buildBottomBar(viewModel);
        },
      ),
    );
  }

  /// 构建状态栏
  Widget _buildStatusBar(DeviceConfigViewModel viewModel) {
    Color backgroundColor;
    String statusText;
    IconData statusIcon;

    if (!viewModel.hasConnectedDevice) {
      backgroundColor = Colors.red.withOpacity(0.1);
      statusText = '设备未连接';
      statusIcon = Icons.bluetooth_disabled;
    } else if (!viewModel.isReady) {
      backgroundColor = Colors.orange.withOpacity(0.1);
      statusText = '设备连接中...';
      statusIcon = Icons.bluetooth_searching;
    } else if (viewModel.isConfiguring) {
      backgroundColor = Colors.blue.withOpacity(0.1);
      statusText = '正在配置WiFi...';
      statusIcon = Icons.settings;
    } else {
      backgroundColor = Colors.green.withOpacity(0.1);
      statusText = '设备已就绪';
      statusIcon = Icons.bluetooth_connected;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: backgroundColor,
      child: Row(
        children: [
          Icon(statusIcon, size: 20),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusText,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                if (viewModel.connectedDevice != null)
                  Text(
                    '设备: ${viewModel.connectedDevice!.name}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(DeviceConfigViewModel viewModel) {
    if (!viewModel.hasConnectedDevice) {
      return const ErrorIndicator(
        message: '设备未连接\n请返回重新连接设备',
      );
    }

    if (!viewModel.isReady && !viewModel.isConfiguring) {
      return const LoadingIndicator(
        message: '正在建立连接...',
      );
    }

    if (viewModel.errorMessage != null) {
      return ErrorIndicator(
        message: viewModel.errorMessage!,
        onRetry: () => _retryConfiguration(viewModel),
        retryLabel: '重试',
      );
    }

    return WiFiConfigForm(
      selectedMode: viewModel.selectedWifiMode,
      staSsid: viewModel.staSsid,
      staPassword: viewModel.staPassword,
      softApSsid: viewModel.softApSsid,
      softApPassword: viewModel.softApPassword,
      softApAuthMode: viewModel.softApAuthMode,
      softApChannel: viewModel.softApChannel,
      softApMaxConnections: viewModel.softApMaxConnections,
      availableNetworks: viewModel.availableWifiNetworks,
      onModeChanged: viewModel.setWifiMode,
      onStaSsidChanged: (value) => viewModel.setStationConfig(ssid: value),
      onStaPasswordChanged: (value) =>
          viewModel.setStationConfig(password: value),
      onSoftApSsidChanged: (value) => viewModel.setSoftApConfig(ssid: value),
      onSoftApPasswordChanged: (value) =>
          viewModel.setSoftApConfig(password: value),
      onSoftApAuthModeChanged: (value) =>
          viewModel.setSoftApConfig(authMode: value),
      onSoftApChannelChanged: (value) =>
          viewModel.setSoftApConfig(channel: value),
      onSoftApMaxConnectionsChanged: (value) =>
          viewModel.setSoftApConfig(maxConnections: value),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(DeviceConfigViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => _resetConfiguration(viewModel),
                child: const Text('重置'),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: viewModel.canConfigure
                    ? () => _configureWifi(viewModel)
                    : null,
                child: viewModel.isConfiguring
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('配置WiFi'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取WiFi列表
  Future<void> _getWifiList(DeviceConfigViewModel viewModel) async {
    try {
      await viewModel.getWifiList();
    } catch (e) {
      _showMessage('获取WiFi列表失败: $e');
    }
  }

  /// 配置WiFi
  Future<void> _configureWifi(DeviceConfigViewModel viewModel) async {
    try {
      final success = await viewModel.configureWifi();

      if (success && mounted) {
        _showMessage('WiFi配置已发送');

        // 延迟一段时间后导航到状态页面
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const ConnectionStatusPageProvider(),
              ),
            );
          }
        });
      }
    } catch (e) {
      _showMessage('配置失败: $e');
    }
  }

  /// 重置配置
  void _resetConfiguration(DeviceConfigViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置配置'),
        content: const Text('确定要重置所有WiFi配置吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              viewModel.resetConfiguration();
              _showMessage('配置已重置');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 重试配置
  void _retryConfiguration(DeviceConfigViewModel viewModel) {
    // 可以在这里添加重试逻辑
    _showMessage('请检查配置后重试');
  }

  /// 显示安全设置
  void _showSecuritySettings(DeviceConfigViewModel viewModel) {
    // 获取安全管理器
    final securityManager = viewModel.getSecurityManager();
    if (securityManager != null) {
      SecuritySettingsDialog.show(context, securityManager);
    } else {
      _showMessage('安全管理器不可用');
    }
  }

  /// 调试连接状态
  void _debugConnection(DeviceConfigViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('连接调试信息'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('设备连接状态: ${viewModel.hasConnectedDevice ? "已连接" : "未连接"}'),
              Text('服务就绪状态: ${viewModel.isReady ? "就绪" : "未就绪"}'),
              Text('配置状态: ${viewModel.isConfiguring ? "配置中" : "空闲"}'),
              if (viewModel.errorMessage != null)
                Text('错误信息: ${viewModel.errorMessage}'),
              const SizedBox(height: 16),
              const Text('操作:'),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  viewModel.forceRefreshState();
                  Navigator.of(context).pop();
                  _showMessage('已强制刷新状态');
                },
                child: const Text('强制刷新状态'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 显示消息
  void _showMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

/// 设备配置页面的Provider包装器
class DeviceConfigPageProvider extends StatelessWidget {
  const DeviceConfigPageProvider({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => DeviceConfigViewModel(
        context.read(), // BluFiService
      ),
      child: const DeviceConfigPage(),
    );
  }
}
