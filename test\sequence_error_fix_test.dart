import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:esp32_blufi/services/sequence_control_service.dart';
import 'package:esp32_blufi/services/encryption_service.dart';
import 'package:esp32_blufi/services/frame_parser.dart';

void main() {
  group('Sequence Error Fix Tests', () {
    late SequenceControlService sequenceControl;
    late EncryptionService encryptionService;
    late FrameParser frameParser;

    setUp(() {
      sequenceControl = SequenceControlService();
      encryptionService = EncryptionService();
      frameParser = FrameParser();
    });

    test('Sequence validation should be more strict', () {
      // 重置序列控制
      sequenceControl.reset();
      
      // 第一个序列号应该被接受
      expect(sequenceControl.validateReceiveSequence(0), isTrue);
      
      // 下一个序列号应该被接受
      expect(sequenceControl.validateReceiveSequence(1), isTrue);
      
      // 跳跃太大的序列号应该被拒绝
      expect(sequenceControl.validateReceiveSequence(20), isFalse);
      
      // 回到合理范围内的序列号应该被接受
      expect(sequenceControl.validateReceiveSequence(2), isTrue);
    });

    test('Sequence validation should handle wrap-around', () {
      sequenceControl.reset();
      
      // 模拟序列号接近最大值
      for (int i = 0; i < 250; i++) {
        sequenceControl.validateReceiveSequence(i);
      }
      
      // 序列号255应该被接受
      expect(sequenceControl.validateReceiveSequence(255), isTrue);
      
      // 回绕到0应该被接受
      expect(sequenceControl.validateReceiveSequence(0), isTrue);
      
      // 继续递增应该被接受
      expect(sequenceControl.validateReceiveSequence(1), isTrue);
    });

    test('IV generation should be consistent', () {
      encryptionService.initializeDH();
      final publicKey = encryptionService.getPublicKeyBytes()!;
      encryptionService.computeSharedSecret(publicKey);
      
      final testData = Uint8List.fromList('test'.codeUnits);
      
      // 使用相同序列号加密两次，应该产生相同结果
      final encrypted1 = encryptionService.encrypt(testData, 5);
      final encrypted2 = encryptionService.encrypt(testData, 5);
      
      expect(encrypted1, isNotNull);
      expect(encrypted2, isNotNull);
      expect(encrypted1, equals(encrypted2));
      
      // 使用不同序列号应该产生不同结果
      final encrypted3 = encryptionService.encrypt(testData, 6);
      expect(encrypted3, isNotNull);
      expect(encrypted3, isNot(equals(encrypted1)));
    });

    test('Encryption and decryption should work with correct sequence', () {
      encryptionService.initializeDH();
      final publicKey = encryptionService.getPublicKeyBytes()!;
      encryptionService.computeSharedSecret(publicKey);
      
      final testData = Uint8List.fromList('Hello BluFi Sequence Test!'.codeUnits);
      const sequence = 42;
      
      // 加密
      final encrypted = encryptionService.encrypt(testData, sequence);
      expect(encrypted, isNotNull);
      
      // 使用相同序列号解密
      final decrypted = encryptionService.decrypt(encrypted!, sequence);
      expect(decrypted, isNotNull);
      expect(decrypted, equals(testData));
      
      // 使用错误序列号解密应该失败或产生错误数据
      final wrongDecrypted = encryptionService.decrypt(encrypted, sequence + 1);
      if (wrongDecrypted != null) {
        // 如果解密没有失败，至少数据应该不同
        expect(wrongDecrypted, isNot(equals(testData)));
      }
    });

    test('Frame parser should maintain sequence consistency', () {
      frameParser.resetSequence();
      
      // 创建多个帧
      final frames = [
        frameParser.createGetVersionFrame(),
        frameParser.createGetWifiStatusFrame(),
        frameParser.createStaSsidFrame('TestSSID'),
        frameParser.createStaPasswordFrame('TestPassword'),
      ];
      
      // 验证序列号连续
      for (int i = 0; i < frames.length; i++) {
        expect(frames[i].sequence, equals(i), 
               reason: 'Frame $i should have sequence $i');
      }
    });

    test('Sequence error should be properly detected', () {
      sequenceControl.reset();
      
      // 正常序列
      expect(sequenceControl.validateReceiveSequence(0), isTrue);
      expect(sequenceControl.validateReceiveSequence(1), isTrue);
      expect(sequenceControl.validateReceiveSequence(2), isTrue);
      
      // 跳跃过大的序列号应该被拒绝
      expect(sequenceControl.validateReceiveSequence(50), isFalse);
      
      // 回到正常范围
      expect(sequenceControl.validateReceiveSequence(3), isTrue);
      expect(sequenceControl.validateReceiveSequence(4), isTrue);
    });

    test('Sequence tolerance should be configurable', () {
      sequenceControl.reset();
      
      // 在容忍范围内的序列号应该被接受
      expect(sequenceControl.validateReceiveSequence(0), isTrue);
      expect(sequenceControl.validateReceiveSequence(5), isTrue); // 跳跃5，在容忍范围内
      expect(sequenceControl.validateReceiveSequence(10), isTrue); // 跳跃5，在容忍范围内
      
      // 超出容忍范围的序列号应该被拒绝
      expect(sequenceControl.validateReceiveSequence(25), isFalse); // 跳跃15，超出容忍范围
    });

    test('Reset should clear sequence state properly', () {
      // 使用一些序列号
      sequenceControl.validateReceiveSequence(10);
      sequenceControl.validateReceiveSequence(11);
      sequenceControl.getNextSendSequence();
      sequenceControl.getNextSendSequence();
      
      // 重置
      sequenceControl.reset();
      
      // 重置后应该从0开始
      expect(sequenceControl.getNextSendSequence(), equals(0));
      expect(sequenceControl.validateReceiveSequence(0), isTrue);
      expect(sequenceControl.validateReceiveSequence(1), isTrue);
    });

    tearDown(() {
      encryptionService.reset();
    });
  });
}
