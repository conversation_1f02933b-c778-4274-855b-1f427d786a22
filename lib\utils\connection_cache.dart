import 'package:logger/logger.dart';

/// 连接状态缓存管理器
/// 用于优化蓝牙连接性能，避免频繁重试和记录设备连接历史
class ConnectionStateCache {
  static final Logger _logger = Logger();
  
  // 最后连接尝试时间记录
  static final Map<String, DateTime> _lastConnectionAttempts = {};
  
  // 已知良好设备记录
  static final Map<String, bool> _knownGoodDevices = {};
  
  // 连接失败计数
  static final Map<String, int> _failureCount = {};
  
  // 设备连接性能记录
  static final Map<String, ConnectionPerformance> _performanceHistory = {};
  
  /// 检查设备是否为已知良好设备
  static bool isKnownGoodDevice(String deviceId) {
    return _knownGoodDevices[deviceId] ?? false;
  }
  
  /// 标记设备为良好设备
  static void markAsGoodDevice(String deviceId) {
    _knownGoodDevices[deviceId] = true;
    _failureCount[deviceId] = 0; // 重置失败计数
    _logger.i('Device $deviceId marked as known good device');
  }
  
  /// 检查是否应该跳过连接尝试（避免频繁重试）
  static bool shouldSkipConnection(String deviceId) {
    final lastAttempt = _lastConnectionAttempts[deviceId];
    if (lastAttempt == null) return false;
    
    final timeSinceLastAttempt = DateTime.now().difference(lastAttempt).inSeconds;
    final failureCount = _failureCount[deviceId] ?? 0;
    
    // 根据失败次数动态调整等待时间
    final minWaitTime = failureCount > 3 ? 30 : (failureCount > 1 ? 10 : 5);
    
    if (timeSinceLastAttempt < minWaitTime) {
      _logger.w('Skipping connection to $deviceId, last attempt was ${timeSinceLastAttempt}s ago');
      return true;
    }
    
    return false;
  }
  
  /// 记录连接尝试
  static void recordConnectionAttempt(String deviceId) {
    _lastConnectionAttempts[deviceId] = DateTime.now();
    _logger.d('Recorded connection attempt for device $deviceId');
  }
  
  /// 记录连接失败
  static void recordConnectionFailure(String deviceId, String reason) {
    _failureCount[deviceId] = (_failureCount[deviceId] ?? 0) + 1;
    _knownGoodDevices[deviceId] = false;
    
    final failureCount = _failureCount[deviceId]!;
    _logger.w('Connection failure for $deviceId (count: $failureCount): $reason');
    
    // 更新性能记录
    final performance = _performanceHistory[deviceId] ?? ConnectionPerformance();
    performance.recordFailure(reason);
    _performanceHistory[deviceId] = performance;
  }
  
  /// 记录连接成功及耗时
  static void recordConnectionSuccess(String deviceId, Duration connectionTime) {
    _failureCount[deviceId] = 0;
    _knownGoodDevices[deviceId] = true;
    
    _logger.i('Connection success for $deviceId in ${connectionTime.inMilliseconds}ms');
    
    // 更新性能记录
    final performance = _performanceHistory[deviceId] ?? ConnectionPerformance();
    performance.recordSuccess(connectionTime);
    _performanceHistory[deviceId] = performance;
  }
  
  /// 获取设备连接性能统计
  static ConnectionPerformance? getPerformanceStats(String deviceId) {
    return _performanceHistory[deviceId];
  }
  
  /// 获取推荐的连接超时时间
  static Duration getRecommendedTimeout(String deviceId) {
    final performance = _performanceHistory[deviceId];
    if (performance == null || performance.averageConnectionTime == null) {
      return const Duration(seconds: 3); // 默认超时
    }
    
    // 基于历史平均时间设置超时，增加50%的缓冲
    final avgMs = performance.averageConnectionTime!.inMilliseconds;
    final timeoutMs = (avgMs * 1.5).round().clamp(2000, 8000); // 2-8秒范围
    
    return Duration(milliseconds: timeoutMs);
  }
  
  /// 清理过期的缓存数据
  static void cleanup() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    // 清理7天前的连接尝试记录
    _lastConnectionAttempts.forEach((deviceId, lastAttempt) {
      if (now.difference(lastAttempt).inDays > 7) {
        expiredKeys.add(deviceId);
      }
    });
    
    for (final key in expiredKeys) {
      _lastConnectionAttempts.remove(key);
      _failureCount.remove(key);
      _performanceHistory.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.i('Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }
  
  /// 重置设备的缓存状态
  static void resetDevice(String deviceId) {
    _lastConnectionAttempts.remove(deviceId);
    _knownGoodDevices.remove(deviceId);
    _failureCount.remove(deviceId);
    _performanceHistory.remove(deviceId);
    _logger.i('Reset cache for device $deviceId');
  }
  
  /// 获取所有缓存统计信息
  static Map<String, dynamic> getStats() {
    return {
      'totalDevices': _performanceHistory.length,
      'knownGoodDevices': _knownGoodDevices.values.where((v) => v).length,
      'devicesWithFailures': _failureCount.values.where((v) => v > 0).length,
      'averageConnectionTime': _calculateOverallAverageTime(),
    };
  }
  
  static Duration? _calculateOverallAverageTime() {
    final times = _performanceHistory.values
        .where((p) => p.averageConnectionTime != null)
        .map((p) => p.averageConnectionTime!.inMilliseconds)
        .toList();
    
    if (times.isEmpty) return null;
    
    final avgMs = times.reduce((a, b) => a + b) / times.length;
    return Duration(milliseconds: avgMs.round());
  }
}

/// 设备连接性能记录
class ConnectionPerformance {
  final List<Duration> _connectionTimes = [];
  final List<String> _failureReasons = [];
  int _successCount = 0;
  int _failureCount = 0;
  
  /// 记录成功连接
  void recordSuccess(Duration connectionTime) {
    _connectionTimes.add(connectionTime);
    _successCount++;
    
    // 只保留最近20次记录
    if (_connectionTimes.length > 20) {
      _connectionTimes.removeAt(0);
    }
  }
  
  /// 记录连接失败
  void recordFailure(String reason) {
    _failureReasons.add(reason);
    _failureCount++;
    
    // 只保留最近10次失败记录
    if (_failureReasons.length > 10) {
      _failureReasons.removeAt(0);
    }
  }
  
  /// 获取平均连接时间
  Duration? get averageConnectionTime {
    if (_connectionTimes.isEmpty) return null;
    
    final totalMs = _connectionTimes
        .map((d) => d.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: (totalMs / _connectionTimes.length).round());
  }
  
  /// 获取成功率
  double get successRate {
    final total = _successCount + _failureCount;
    return total == 0 ? 0.0 : _successCount / total;
  }
  
  /// 获取最快连接时间
  Duration? get fastestConnectionTime {
    if (_connectionTimes.isEmpty) return null;
    return _connectionTimes.reduce((a, b) => a.inMilliseconds < b.inMilliseconds ? a : b);
  }
  
  /// 获取最慢连接时间
  Duration? get slowestConnectionTime {
    if (_connectionTimes.isEmpty) return null;
    return _connectionTimes.reduce((a, b) => a.inMilliseconds > b.inMilliseconds ? a : b);
  }
  
  /// 获取最近的失败原因
  List<String> get recentFailureReasons => List.unmodifiable(_failureReasons);
  
  /// 获取统计摘要
  Map<String, dynamic> toMap() {
    return {
      'successCount': _successCount,
      'failureCount': _failureCount,
      'successRate': successRate,
      'averageConnectionTime': averageConnectionTime?.inMilliseconds,
      'fastestConnectionTime': fastestConnectionTime?.inMilliseconds,
      'slowestConnectionTime': slowestConnectionTime?.inMilliseconds,
      'recentFailures': _failureReasons.take(5).toList(),
    };
  }
}
