import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../utils/error_messages.dart';

/// 增强的错误显示组件
class ErrorDisplay extends StatelessWidget {
  final String errorMessage;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showSuggestions;

  const ErrorDisplay({
    Key? key,
    required this.errorMessage,
    this.onRetry,
    this.onDismiss,
    this.showSuggestions = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 尝试从错误消息中提取错误代码
    final errorCode = _extractErrorCode(errorMessage);
    final severity = errorCode != null 
        ? BluFiErrorMessages.getErrorSeverity(errorCode)
        : ErrorSeverity.unknown;
    final suggestions = errorCode != null 
        ? BluFiErrorMessages.getErrorSuggestions(errorCode)
        : <String>[];

    return Card(
      color: Color(severity.colorValue).withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 错误标题
            Row(
              children: [
                Icon(
                  _getErrorIcon(severity),
                  color: Color(severity.colorValue),
                  size: 24,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    '${severity.description}错误',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Color(severity.colorValue),
                    ),
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    onPressed: onDismiss,
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // 错误消息
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[800],
              ),
            ),
            
            // 建议操作
            if (showSuggestions && suggestions.isNotEmpty) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                '建议操作:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              ...suggestions.map((suggestion) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[600],
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        suggestion,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
            
            // 操作按钮
            if (onRetry != null) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: const Text('重试'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Color(severity.colorValue),
                      side: BorderSide(color: Color(severity.colorValue)),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 从错误消息中提取错误代码
  int? _extractErrorCode(String message) {
    // 尝试匹配 "代码: 0x" 或 "(0x" 格式的错误代码
    final regex = RegExp(r'(?:代码|code):\s*(?:0x)?([0-9a-fA-F]+)|(?:\(0x([0-9a-fA-F]+)\))');
    final match = regex.firstMatch(message);
    
    if (match != null) {
      final codeStr = match.group(1) ?? match.group(2);
      if (codeStr != null) {
        return int.tryParse(codeStr, radix: 16);
      }
    }
    
    return null;
  }

  /// 根据严重程度获取图标
  IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.warning:
        return Icons.warning_amber;
      case ErrorSeverity.recoverable:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.error;
      case ErrorSeverity.fatal:
        return Icons.dangerous;
      case ErrorSeverity.unknown:
        return Icons.help_outline;
    }
  }
}

/// 简化的错误显示组件
class SimpleErrorDisplay extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final String? retryLabel;

  const SimpleErrorDisplay({
    Key? key,
    required this.message,
    this.onRetry,
    this.retryLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red[600]),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red[800],
                  ),
                ),
              ),
            ],
          ),
          if (onRetry != null) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh, size: 18),
                  label: Text(retryLabel ?? '重试'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red[600],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// 错误Toast显示工具
class ErrorToast {
  /// 显示错误Toast
  static void show(BuildContext context, String message, {
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onRetry,
  }) {
    final errorCode = _extractErrorCode(message);
    final severity = errorCode != null 
        ? BluFiErrorMessages.getErrorSeverity(errorCode)
        : ErrorSeverity.unknown;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getErrorIcon(severity),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Color(severity.colorValue),
        duration: duration,
        action: onRetry != null
            ? SnackBarAction(
                label: '重试',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// 从错误消息中提取错误代码
  static int? _extractErrorCode(String message) {
    final regex = RegExp(r'(?:代码|code):\s*(?:0x)?([0-9a-fA-F]+)|(?:\(0x([0-9a-fA-F]+)\))');
    final match = regex.firstMatch(message);
    
    if (match != null) {
      final codeStr = match.group(1) ?? match.group(2);
      if (codeStr != null) {
        return int.tryParse(codeStr, radix: 16);
      }
    }
    
    return null;
  }

  /// 根据严重程度获取图标
  static IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.warning:
        return Icons.warning_amber;
      case ErrorSeverity.recoverable:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.error;
      case ErrorSeverity.fatal:
        return Icons.dangerous;
      case ErrorSeverity.unknown:
        return Icons.help_outline;
    }
  }
}
