import 'package:flutter/material.dart';
import '../models/wifi_config.dart';
import '../utils/blufi_constants.dart';

/// 配置选项页面
/// 模仿Android版本的ConfigureOptionsActivity
class ConfigureOptionsPage extends StatefulWidget {
  const ConfigureOptionsPage({super.key});

  @override
  State<ConfigureOptionsPage> createState() => _ConfigureOptionsPageState();
}

class _ConfigureOptionsPageState extends State<ConfigureOptionsPage> {
  // 操作模式选项
  static const List<String> _opModeOptions = [
    'Station',
    'SoftAP',
    'Station + SoftAP',
  ];

  static const List<int> _opModeValues = [
    BluFiConstants.wifiModeSta,
    BluFiConstants.wifiModeAp,
    BluFiConstants.wifiModeApSta,
  ];

  // SoftAP安全模式选项
  static const List<String> _securityOptions = [
    'Open',
    'WPA',
    'WPA2',
    'WPA/WPA2',
  ];

  static const List<int> _securityValues = [
    BluFiConstants.wifiAuthOpen,
    BluFiConstants.wifiAuthWpaPsk,
    BluFiConstants.wifiAuthWpa2Psk,
    BluFiConstants.wifiAuthWpaWpa2Psk,
  ];

  // 表单控制器
  final _stationSsidController = TextEditingController();
  final _stationPasswordController = TextEditingController();
  final _softApSsidController = TextEditingController();
  final _softApPasswordController = TextEditingController();

  // 选择状态
  int _selectedOpMode = 0;
  int _selectedSecurity = 0;
  int _selectedChannel = 1;
  int _selectedMaxConnections = 4;

  // 密码可见性状态
  bool _stationPasswordVisible = false;
  bool _softApPasswordVisible = false;

  @override
  void dispose() {
    _stationSsidController.dispose();
    _stationPasswordController.dispose();
    _softApSsidController.dispose();
    _softApPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configure Options'),
        actions: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: ElevatedButton.icon(
              onPressed: _onConfirm,
              icon: const Icon(Icons.check, size: 18),
              label: const Text('CONFIRM'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                elevation: 2,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDeviceModeSection(),
            const SizedBox(height: 24),
            if (_showStationOptions()) _buildStationSection(),
            if (_showSoftApOptions()) _buildSoftApSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceModeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Device Mode',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _selectedOpMode,
              decoration: const InputDecoration(
                labelText: 'Operation Mode',
                border: OutlineInputBorder(),
              ),
              items: List.generate(_opModeOptions.length, (index) {
                return DropdownMenuItem(
                  value: index,
                  child: Text(_opModeOptions[index]),
                );
              }),
              onChanged: (value) {
                setState(() {
                  _selectedOpMode = value ?? 0;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Station Configuration',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _stationSsidController,
              decoration: InputDecoration(
                labelText: 'WiFi SSID',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.wifi_find),
                  onPressed: _scanWifi,
                  tooltip: 'Scan WiFi',
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter WiFi SSID';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _stationPasswordController,
              decoration: InputDecoration(
                labelText: 'WiFi Password',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _stationPasswordVisible
                        ? Icons.visibility
                        : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _stationPasswordVisible = !_stationPasswordVisible;
                    });
                  },
                  tooltip: _stationPasswordVisible
                      ? 'Hide password'
                      : 'Show password',
                ),
              ),
              obscureText: !_stationPasswordVisible,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSoftApSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'SoftAP Configuration',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _softApSsidController,
              decoration: const InputDecoration(
                labelText: 'SoftAP SSID',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _selectedSecurity,
              decoration: const InputDecoration(
                labelText: 'Security',
                border: OutlineInputBorder(),
              ),
              items: List.generate(_securityOptions.length, (index) {
                return DropdownMenuItem(
                  value: index,
                  child: Text(_securityOptions[index]),
                );
              }),
              onChanged: (value) {
                setState(() {
                  _selectedSecurity = value ?? 0;
                });
              },
            ),
            if (_selectedSecurity > 0) ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: _softApPasswordController,
                decoration: InputDecoration(
                  labelText: 'SoftAP Password',
                  border: const OutlineInputBorder(),
                  helperText: 'Minimum 8 characters',
                  suffixIcon: IconButton(
                    icon: Icon(
                      _softApPasswordVisible
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _softApPasswordVisible = !_softApPasswordVisible;
                      });
                    },
                    tooltip: _softApPasswordVisible
                        ? 'Hide password'
                        : 'Show password',
                  ),
                ),
                obscureText: !_softApPasswordVisible,
                validator: (value) {
                  if (_selectedSecurity > 0 &&
                      (value == null || value.length < 8)) {
                    return 'Password must be at least 8 characters';
                  }
                  return null;
                },
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedChannel,
                    decoration: const InputDecoration(
                      labelText: 'Channel',
                      border: OutlineInputBorder(),
                    ),
                    items: List.generate(13, (index) {
                      return DropdownMenuItem(
                        value: index + 1,
                        child: Text('${index + 1}'),
                      );
                    }),
                    onChanged: (value) {
                      setState(() {
                        _selectedChannel = value ?? 1;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedMaxConnections,
                    decoration: const InputDecoration(
                      labelText: 'Max Connections',
                      border: OutlineInputBorder(),
                    ),
                    items: List.generate(8, (index) {
                      return DropdownMenuItem(
                        value: index + 1,
                        child: Text('${index + 1}'),
                      );
                    }),
                    onChanged: (value) {
                      setState(() {
                        _selectedMaxConnections = value ?? 4;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool _showStationOptions() {
    return _selectedOpMode == 0 || _selectedOpMode == 2; // STA or STA+SoftAP
  }

  bool _showSoftApOptions() {
    return _selectedOpMode == 1 || _selectedOpMode == 2; // SoftAP or STA+SoftAP
  }

  void _scanWifi() {
    // TODO: 实现WiFi扫描功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('WiFi scan functionality will be implemented'),
      ),
    );
  }

  void _onConfirm() {
    // 验证表单
    if (!_validateForm()) {
      return;
    }

    // 创建配置参数
    final config = _createWiFiConfig();

    // 返回配置结果
    Navigator.of(context).pop(config);
  }

  bool _validateForm() {
    if (_showStationOptions()) {
      if (_stationSsidController.text.isEmpty) {
        _showError('Please enter WiFi SSID');
        return false;
      }
    }

    if (_showSoftApOptions()) {
      if (_softApSsidController.text.isEmpty) {
        _showError('Please enter SoftAP SSID');
        return false;
      }

      if (_selectedSecurity > 0 && _softApPasswordController.text.length < 8) {
        _showError('SoftAP password must be at least 8 characters');
        return false;
      }
    }

    return true;
  }

  WiFiConfig _createWiFiConfig() {
    StationConfig? stationConfig;
    SoftApConfig? softApConfig;

    if (_showStationOptions()) {
      stationConfig = StationConfig(
        ssid: _stationSsidController.text,
        password: _stationPasswordController.text,
      );
    }

    if (_showSoftApOptions()) {
      softApConfig = SoftApConfig(
        ssid: _softApSsidController.text,
        password: _softApPasswordController.text,
        authMode: _securityValues[_selectedSecurity],
        channel: _selectedChannel,
        maxConnections: _selectedMaxConnections,
      );
    }

    return WiFiConfig(
      opMode: _opModeValues[_selectedOpMode],
      stationConfig: stationConfig,
      softApConfig: softApConfig,
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
