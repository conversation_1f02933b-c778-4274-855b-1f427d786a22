import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:logger/logger.dart'; // 核心日志库

class BlufiService {
  static final Logger _logger = Logger();
  // 添加一个Completer来处理ACK
  Completer<bool>? _commandCompleter;
  StreamSubscription<List<int>>? _dataSubscription;

  // UUID相关
  static const String BLUFI_SERVICE_UUID =
      "0000ffff-0000-1000-8000-00805f9b34fb";
  static const String WRITE_CHAR_UUID = "0000ff01-0000-1000-8000-00805f9b34fb";
  static const String NOTIFY_CHAR_UUID = "0000ff02-0000-1000-8000-00805f9b34fb";
  // 帧类型定义
  static const int TYPE_CTRL = 0x0;
  static const int TYPE_DATA = 0x1;

  // 子类型定义 ?应该做一个枚举类型
  static const int SUBTYPE_VERSION = 0x7;
  static const int SUBTYPE_WIFI_STATUS = 0x5;
  static const int SUBTYPE_CUSTOM_DATA = 0x13;

  // 创建获取版本信息的帧
  static List<int> createVersionFrame() {
    return _createFrame(TYPE_DATA, SUBTYPE_VERSION, []);
  }

  // 创建获取WiFi状态的帧
  static List<int> createWifiStatusFrame() {
    return _createFrame(TYPE_CTRL, SUBTYPE_WIFI_STATUS, []);
  }

  // 创建自定义数据帧
  List<int> createCustomDataFrame(List<int> data) {
    return _createFrame(TYPE_DATA, SUBTYPE_CUSTOM_DATA, data);
  }

  // 创建帧
  static List<int> _createFrame(int type, int subType, List<int> data) {
    List<int> frame = [];
    frame.add((type & 0x1) | ((subType & 0x3f) << 2)); // 类型和子类型
    frame.add(0x8); // 帧控制
    frame.add(_getSequence()); // 序列号
    frame.add(data.length); // 数据长度
    frame.addAll(data); // 数据
    // frame.addAll(_calculateChecksum(frame)); // 添加效验数据
    return frame;
  }

  // 获取序列号
  static int _sequence = 0;
  static void resetSequence(bool isReset) {
    if (isReset) {
      _sequence = 0;
    } else {
      _sequence = (_sequence - 1) & 0xff;
    }
  }

  static int _getSequence() {
    int currentSequence = _sequence & 0xff;
    _sequence = (_sequence + 1) & 0xff;
    return currentSequence;
  }

  // 计算校验，BLufi的效验比较复杂，暂时不实现

  // 解析响应
  static Map<String, dynamic> parseResponse(List<int> data) {
    if (data.length < 4) return {'error': '数据长度不足'};

    final type = data[0] & 0x1;
    final subType = (data[0] >> 2) & 0x3f;
    final frameCtrl = data[1];
    final sequence = data[2];
    final dataLen = data[3];

    Map<String, dynamic> result = {
      'type': type,
      'subType': subType,
      'frameCtrl': frameCtrl,
      'sequence': sequence,
      'dataLength': dataLen,
    };

    if (data.length > 4) {
      result['data'] = data.sublist(4, 4 + dataLen);
    }

    _logger.i(
        'BlufiService收到原始数据(HEX): ${data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(',')}');

    // ASCII格式打印
    String asciiText = String.fromCharCodes(data);
    _logger.i('BlufiService收到ASCII数据: $asciiText');

    // 如果需要同时显示ASCII码值
    String asciiValues = data.map((b) => 'ASCII($b)').join(',');
    _logger.i('BlufiService收到ASCII码值: $asciiValues');

    return result;
  }

  // 设置数据监听
  Future<void> setupBlufiDataListener(BluetoothDevice device) async {
    final services = await device.discoverServices();
    for (var service in services) {
      if (service.uuid == Guid(BLUFI_SERVICE_UUID)) {
        for (var char in service.characteristics) {
          if (char.uuid == Guid(NOTIFY_CHAR_UUID)) {
            await char.setNotifyValue(true);
            _dataSubscription = char.onValueReceived.listen(
              (value) {
                if (value.isNotEmpty) {
                  _logger.i('收到数据: $value');
                  final response = BlufiService.parseResponse(value);
                  _logger.i('收到数据: $response');
                  if (response['subType'] << 2 | response['type'] == 0x3d &&
                      response['dataLength'] != 0) {
                    // 处理信息
                  } else if (response['subType'] << 2 | response['type'] ==
                      0x49) {
                    _commandCompleter?.complete(false);

                    BlufiService.resetSequence(false);
                  } else if (response['subType'] << 2 | response['type'] ==
                      0x00) {
                    _commandCompleter?.complete(true);
                  }
                }
              },
              onError: (error) {
                _logger.e('数据监听错误: $error', error: error);
                _commandCompleter?.complete(false);
              },
            );
            return;
          }
        }
      }
    }
  }

  // 在不需要时取消对Blufi的监听
  void disposeBlufiDataListener() {
    _dataSubscription?.cancel();
    _dataSubscription = null;
  }

  // 通过蓝牙发送数据
  Future<void> writeBlufiData(BluetoothDevice device, List<int> data) async {
    try {
      final services = await device.discoverServices();
      for (var service in services) {
        if (service.uuid == Guid(BLUFI_SERVICE_UUID)) {
          for (var char in service.characteristics) {
            if (char.uuid == Guid(WRITE_CHAR_UUID)) {
              await char.write(data);
              return;
            }
          }
        }
      }
    } catch (e, stackTrace) {
      _logger.w('发送蓝牙数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
}
