import 'package:logger/logger.dart';

/// 序列控制服务
/// 根据ESP-IDF BluFi文档实现：
/// "When a frame is sent, the value of this field is automatically incremented by 1
/// regardless of the type of frame, which prevents Replay Attack.
/// The sequence would be cleared after each reconnection."
class SequenceControlService {
  static final Logger _logger = Logger();

  /// 当前发送序列号 - 每发送一帧自动递增1
  int _sendSequence = 0;

  /// 期望接收的下一个序列号
  int _expectedReceiveSequence = 0;

  /// 已接收的序列号集合（用于防重放攻击）
  final Set<int> _receivedSequences = <int>{};

  /// 序列号最大值（8位，0-255）
  static const int _maxSequenceNumber = 255;

  /// 获取下一个发送序列号
  /// 根据ESP-IDF文档：每发送一帧自动递增1
  /// 注意：第一帧的序列号应该是0，然后递增
  int getNextSendSequence() {
    int currentSequence = _sendSequence;
    _sendSequence = (_sendSequence + 1) % (_maxSequenceNumber + 1);
    _logger.d(
        'Generated send sequence: $currentSequence (next will be $_sendSequence)');
    return currentSequence;
  }

  /// 获取当前发送序列号（不递增）
  int getCurrentSendSequence() {
    return _sendSequence;
  }

  /// 验证接收到的序列号
  /// 根据ESP-IDF文档：防止重放攻击，检查序列号的合理性
  /// 采用适度宽松的策略，允许一定范围内的序列号
  bool validateReceiveSequence(int receivedSequence) {
    _logger.d(
        'Validating received sequence: $receivedSequence, expected around: $_expectedReceiveSequence');

    // 检查序列号是否在合理范围内
    // 允许序列号在期望值的前后一定范围内（考虑到可能的重传、乱序等）
    const tolerance = 10; // 允许的序列号偏差范围

    final minAcceptable = (_expectedReceiveSequence - tolerance).clamp(0, 255);
    final maxAcceptable = (_expectedReceiveSequence + tolerance).clamp(0, 255);

    bool isValid = false;

    // 处理序列号回绕的情况（0-255循环）
    if (minAcceptable <= maxAcceptable) {
      // 正常情况：没有回绕
      isValid = receivedSequence >= minAcceptable &&
          receivedSequence <= maxAcceptable;
    } else {
      // 回绕情况：期望序列号接近255，允许范围跨越0
      isValid = receivedSequence >= minAcceptable ||
          receivedSequence <= maxAcceptable;
    }

    if (isValid) {
      // 更新期望的下一个序列号
      _expectedReceiveSequence = (receivedSequence + 1) % 256;
      _receivedSequences.add(receivedSequence);

      // 限制集合大小，避免内存泄漏
      if (_receivedSequences.length > 50) {
        final toRemove = _receivedSequences.take(25).toList();
        _receivedSequences.removeAll(toRemove);
      }

      _logger.d(
          'Sequence validation passed: $receivedSequence, next expected: $_expectedReceiveSequence');
    } else {
      _logger.w(
          'Sequence validation failed: $receivedSequence not in range [$minAcceptable, $maxAcceptable]');
    }

    return isValid;
  }

  /// 重置序列控制状态
  /// 根据ESP-IDF文档：每次重新连接后序列号会被清除
  void reset() {
    _logger.i('Resetting sequence control');
    _sendSequence = 0;
    _expectedReceiveSequence = 0;
    _receivedSequences.clear();
    _logger.i('Sequence control reset completed');
  }
}
