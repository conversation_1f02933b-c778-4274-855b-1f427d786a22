import 'dart:math';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:pointycastle/export.dart';
import '../utils/extensions.dart';

/// BluFi加密服务类
/// 实现符合ESP-IDF标准的DH密钥协商和AES加密解密
class EncryptionService {
  static final Logger _logger = Logger();

  // DH参数 (RFC 5114 1024-bit MODP Group)
  static final BigInt _dhP = BigInt.parse(
    'FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1'
    '29024E088A67CC74020BBEA63B139B22514A08798E3404DD'
    'EF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245'
    'E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7ED'
    'EE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3D'
    'C2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F'
    '83655D23DCA3AD961C62F356208552BB9ED529077096966D'
    '670C354E4ABC9804F1746C08CA237327FFFFFFFFFFFFFFFF',
    radix: 16,
  );

  static final BigInt _dhG = BigInt.from(2);

  // 密钥相关
  BigInt? _privateKey;
  BigInt? _publicKey;
  BigInt? _sharedSecret;
  Uint8List? _aesKey;

  /// 初始化DH密钥对
  void initializeDH() {
    final random = Random.secure();

    // 生成私钥 (256位随机数)
    _privateKey = _generateRandomBigInt(256, random);

    // 计算公钥: g^privateKey mod p
    _publicKey = _dhG.modPow(_privateKey!, _dhP);

    _logger.d('DH key pair initialized');
  }

  /// 检查是否已初始化
  bool get isInitialized => _publicKey != null && _privateKey != null;

  /// 获取公钥字节数组
  Uint8List? getPublicKeyBytes() {
    if (_publicKey == null) return null;

    final keyBytes = _bigIntToBytes(_publicKey!, 128); // 1024位 = 128字节
    return keyBytes;
  }

  /// 计算共享密钥
  bool computeSharedSecret(Uint8List peerPublicKeyBytes) {
    if (_privateKey == null) {
      _logger.e('Private key not initialized');
      return false;
    }

    try {
      final peerPublicKey = _bytesToBigInt(peerPublicKeyBytes);

      // 计算共享密钥: peerPublicKey^privateKey mod p
      _sharedSecret = peerPublicKey.modPow(_privateKey!, _dhP);

      // 从共享密钥派生AES密钥
      _deriveAESKey();

      _logger.d('Shared secret computed successfully');
      return true;
    } catch (e) {
      _logger.e('Error computing shared secret: $e');
      return false;
    }
  }

  /// 从共享密钥派生AES密钥（符合ESP-IDF标准）
  void _deriveAESKey() {
    if (_sharedSecret == null) return;

    // 将共享密钥转换为字节数组
    final secretBytes = _bigIntToBytes(_sharedSecret!, 128);

    // 使用MD5哈希派生AES密钥（符合ESP-IDF实现）
    final md5 = MD5Digest();
    md5.update(secretBytes, 0, secretBytes.length);

    final keyBytes = Uint8List(16);
    md5.doFinal(keyBytes, 0);

    _aesKey = keyBytes;

    _logger.d('AES key derived using MD5 hash (ESP-IDF compatible)');
    _logger.d(
        'AES key: ${_aesKey!.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}');
  }

  /// AES加密（符合ESP-IDF标准）
  Uint8List? encrypt(Uint8List plaintext, int sequence) {
    if (_aesKey == null) {
      _logger.e('AES key not available');
      return null;
    }

    try {
      // 生成序列号相关的IV（ESP-IDF标准方式）
      final iv = _generateSequenceIV(sequence);

      // 使用AES-128-CBC加密
      final cipher = CBCBlockCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(_aesKey!), iv);
      cipher.init(true, params);

      // 添加PKCS7填充
      final paddedPlaintext = _addPKCS7Padding(plaintext);

      // 执行加密
      final encrypted = Uint8List(paddedPlaintext.length);
      int offset = 0;
      while (offset < paddedPlaintext.length) {
        offset +=
            cipher.processBlock(paddedPlaintext, offset, encrypted, offset);
      }

      _logger.d('Encrypted ${plaintext.length} bytes with sequence $sequence');
      return encrypted;
    } catch (e) {
      _logger.e('Encryption error: $e');
      return null;
    }
  }

  /// AES解密（符合ESP-IDF标准）
  Uint8List? decrypt(Uint8List ciphertext, int sequence) {
    if (_aesKey == null) {
      _logger.e('AES key not available');
      return null;
    }

    try {
      // 生成序列号相关的IV（ESP-IDF标准方式）
      final iv = _generateSequenceIV(sequence);

      // 使用AES-128-CBC解密
      final cipher = CBCBlockCipher(AESEngine());
      final params = ParametersWithIV(KeyParameter(_aesKey!), iv);
      cipher.init(false, params);

      // 执行解密
      final decrypted = Uint8List(ciphertext.length);
      int offset = 0;
      while (offset < ciphertext.length) {
        offset += cipher.processBlock(ciphertext, offset, decrypted, offset);
      }

      // 移除PKCS7填充
      final result = _removePKCS7Padding(decrypted);
      _logger.d('Decrypted ${ciphertext.length} bytes with sequence $sequence');
      return result;
    } catch (e) {
      _logger.e('Decryption error: $e');
      return null;
    }
  }

  /// 生成序列号相关的IV（ESP-IDF标准方式）
  Uint8List _generateSequenceIV(int sequence) {
    // 创建16字节IV，前面填0，最后一个字节是序列号
    // 这是更常见的BluFi实现方式
    final iv = Uint8List(16);

    // 前15字节填0
    for (int i = 0; i < 15; i++) {
      iv[i] = 0;
    }

    // 最后一个字节是序列号
    iv[15] = sequence & 0xFF;

    _logger.d(
        'Generated IV for sequence $sequence: ${iv.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}');

    return iv;
  }

  /// 添加PKCS7填充
  Uint8List _addPKCS7Padding(Uint8List data) {
    const blockSize = 16; // AES块大小
    final paddingLength = blockSize - (data.length % blockSize);
    final padded = Uint8List(data.length + paddingLength);

    // 复制原始数据
    padded.setRange(0, data.length, data);

    // 添加填充
    for (int i = data.length; i < padded.length; i++) {
      padded[i] = paddingLength;
    }

    return padded;
  }

  /// 移除PKCS7填充
  Uint8List _removePKCS7Padding(Uint8List data) {
    if (data.isEmpty) return data;

    final paddingLength = data.last;
    if (paddingLength > 16 || paddingLength > data.length) {
      throw const FormatException('Invalid PKCS7 padding');
    }

    // 验证填充
    for (int i = data.length - paddingLength; i < data.length; i++) {
      if (data[i] != paddingLength) {
        throw const FormatException('Invalid PKCS7 padding');
      }
    }

    return Uint8List.fromList(data.sublist(0, data.length - paddingLength));
  }

  /// 计算CRC16校验和
  int calculateCRC16(Uint8List data) {
    return data.calculateCrc16();
  }

  /// 验证CRC16校验和
  bool verifyCRC16(Uint8List data, int expectedCrc) {
    final calculatedCrc = calculateCRC16(data);
    return calculatedCrc == expectedCrc;
  }

  /// 生成随机BigInt
  BigInt _generateRandomBigInt(int bitLength, Random random) {
    final bytes = (bitLength + 7) ~/ 8;
    final randomBytes = Uint8List(bytes);

    for (int i = 0; i < bytes; i++) {
      randomBytes[i] = random.nextInt(256);
    }

    return _bytesToBigInt(randomBytes);
  }

  /// BigInt转字节数组
  Uint8List _bigIntToBytes(BigInt value, int length) {
    final bytes = Uint8List(length);
    var temp = value;

    for (int i = length - 1; i >= 0; i--) {
      bytes[i] = (temp & BigInt.from(0xFF)).toInt();
      temp = temp >> 8;
    }

    return bytes;
  }

  /// 字节数组转BigInt
  BigInt _bytesToBigInt(Uint8List bytes) {
    BigInt result = BigInt.zero;

    for (int i = 0; i < bytes.length; i++) {
      result = (result << 8) + BigInt.from(bytes[i]);
    }

    return result;
  }

  /// 检查是否有共享密钥
  bool get hasSharedSecret => _sharedSecret != null && _aesKey != null;

  /// 重置加密状态
  void reset() {
    _privateKey = null;
    _publicKey = null;
    _sharedSecret = null;
    _aesKey = null;
    _logger.d('Encryption service reset');
  }

  /// 获取调试信息
  Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialized': isInitialized,
      'hasSharedSecret': hasSharedSecret,
      'publicKeyLength':
          _publicKey != null ? getPublicKeyBytes()?.length : null,
      'aesKeyLength': _aesKey?.length,
    };
  }
}
