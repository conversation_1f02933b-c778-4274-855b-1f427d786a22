import 'package:flutter_blue_plus/flutter_blue_plus.dart';

/// BluFi设备模型
class BluFiDevice {
  /// 蓝牙设备对象
  final BluetoothDevice bluetoothDevice;

  /// 设备名称
  final String name;

  /// 设备ID（MAC地址）
  final String deviceId;

  /// 信号强度（RSSI）
  final int rssi;

  /// 是否已连接
  bool isConnected;

  /// 连接时间
  DateTime? connectedAt;

  /// 最后活动时间
  DateTime lastActiveAt;

  /// 设备类型标识
  final String? manufacturerData;

  /// 服务UUID列表
  final List<String> serviceUuids;

  BluFiDevice({
    required this.bluetoothDevice,
    required this.name,
    required this.deviceId,
    required this.rssi,
    this.isConnected = false,
    this.connectedAt,
    DateTime? lastActiveAt,
    this.manufacturerData,
    this.serviceUuids = const [],
  }) : lastActiveAt = lastActiveAt ?? DateTime.now();

  /// 从扫描结果创建BluFi设备
  factory BluFiDevice.fromScanResult(ScanResult scanResult) {
    final device = scanResult.device;
    final advertisementData = scanResult.advertisementData;

    return BluFiDevice(
      bluetoothDevice: device,
      name: advertisementData.advName.isNotEmpty
          ? advertisementData.advName
          : device.platformName.isNotEmpty
              ? device.platformName
              : 'Unknown Device',
      deviceId: device.remoteId.str,
      rssi: scanResult.rssi,
      manufacturerData: advertisementData.manufacturerData.isNotEmpty
          ? advertisementData.manufacturerData.values.first.toString()
          : null,
      serviceUuids: advertisementData.serviceUuids
          .map((uuid) => uuid.toString())
          .toList(),
    );
  }

  /// 更新连接状态
  void updateConnectionState(bool connected) {
    isConnected = connected;
    if (connected) {
      connectedAt = DateTime.now();
    } else {
      connectedAt = null;
    }
    lastActiveAt = DateTime.now();
  }

  /// 更新信号强度
  BluFiDevice copyWithRssi(int newRssi) {
    return BluFiDevice(
      bluetoothDevice: bluetoothDevice,
      name: name,
      deviceId: deviceId,
      rssi: newRssi,
      isConnected: isConnected,
      connectedAt: connectedAt,
      lastActiveAt: DateTime.now(),
      manufacturerData: manufacturerData,
      serviceUuids: serviceUuids,
    );
  }

  /// 获取信号强度描述
  String get rssiDescription {
    if (rssi >= -50) return '优秀';
    if (rssi >= -60) return '良好';
    if (rssi >= -70) return '一般';
    if (rssi >= -80) return '较差';
    return '很差';
  }

  /// 获取连接时长
  Duration? get connectionDuration {
    if (connectedAt == null) return null;
    return DateTime.now().difference(connectedAt!);
  }

  /// 检查是否为BluFi设备
  bool get isBluFiDevice {
    // 检查设备名称是否包含BluFi标识
    final nameLower = name.toLowerCase();
    if (nameLower.contains('blufi') || nameLower.contains('esp32')) {
      return true;
    }

    // 检查是否包含BluFi服务UUID
    return serviceUuids.any((uuid) =>
        uuid.toLowerCase().contains('ffff') ||
        uuid.toLowerCase().contains('0000ffff'));
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BluFiDevice && other.deviceId == deviceId;
  }

  @override
  int get hashCode => deviceId.hashCode;

  @override
  String toString() {
    return 'BluFiDevice{name: $name, deviceId: $deviceId, rssi: $rssi, isConnected: $isConnected}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'deviceId': deviceId,
      'rssi': rssi,
      'isConnected': isConnected,
      'connectedAt': connectedAt?.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'manufacturerData': manufacturerData,
      'serviceUuids': serviceUuids,
    };
  }

  /// 从JSON创建
  factory BluFiDevice.fromJson(
      Map<String, dynamic> json, BluetoothDevice bluetoothDevice) {
    return BluFiDevice(
      bluetoothDevice: bluetoothDevice,
      name: json['name'] ?? 'Unknown Device',
      deviceId: json['deviceId'] ?? '',
      rssi: json['rssi'] ?? -100,
      isConnected: json['isConnected'] ?? false,
      connectedAt: json['connectedAt'] != null
          ? DateTime.parse(json['connectedAt'])
          : null,
      lastActiveAt: json['lastActiveAt'] != null
          ? DateTime.parse(json['lastActiveAt'])
          : DateTime.now(),
      manufacturerData: json['manufacturerData'],
      serviceUuids: List<String>.from(json['serviceUuids'] ?? []),
    );
  }
}
