import 'dart:typed_data';
import 'package:esp32_blufi/services/blufi_security_manager.dart';

/// BluFi加密功能使用示例
/// 展示如何正确使用修复后的加密服务
void main() async {
  print('=== BluFi加密功能示例 ===\n');

  // 1. 创建安全管理器
  final securityManager = BluFiSecurityManager();

  try {
    // 2. 初始化安全管理器
    print('1. 初始化安全管理器...');
    final initResult = await securityManager.initialize();
    if (!initResult) {
      print('❌ 初始化失败');
      return;
    }
    print('✅ 初始化成功');
    print('   状态: ${securityManager.state}');
    print('   已初始化: ${securityManager.hasSharedSecret}');

    // 3. 配置加密选项
    print('\n2. 配置加密选项...');
    securityManager.setEncryptionEnabled(true);
    securityManager.setChecksumEnabled(true);
    print('✅ 加密已启用');
    print('   加密状态: ${securityManager.isEncryptionEnabled}');
    print('   校验状态: ${securityManager.isChecksumEnabled}');

    // 4. 开始密钥协商
    print('\n3. 开始密钥协商...');
    final negotiationData = await securityManager.startKeyNegotiation();
    if (negotiationData == null) {
      print('❌ 密钥协商启动失败');
      return;
    }
    print('✅ 密钥协商启动成功');
    print('   协商数据长度: ${negotiationData.length} 字节');

    // 5. 模拟接收对方公钥（实际应用中这来自ESP32设备）
    print('\n4. 模拟密钥交换...');
    final mockPeerPublicKey = Uint8List(128);
    // 填充一些测试数据（实际应用中这是ESP32的真实公钥）
    for (int i = 0; i < 128; i++) {
      mockPeerPublicKey[i] = (i * 7 + 13) % 256;
    }

    final negotiationResult =
        await securityManager.handleNegotiationData(mockPeerPublicKey);
    if (!negotiationResult) {
      print('❌ 密钥协商失败');
      return;
    }
    print('✅ 密钥协商完成');
    print('   协商状态: ${securityManager.isNegotiationComplete}');
    print('   加密可用: ${securityManager.isEncryptionEnabled}');

    // 6. 获取安全模式配置
    print('\n5. 获取安全模式配置...');
    final securityConfig = securityManager.getSecurityModeConfig();
    print('✅ 安全模式配置获取成功');
    print('   控制帧校验: ${securityConfig['controlFrameChecksum']}');
    print('   控制帧加密: ${securityConfig['controlFrameEncrypt']}');
    print('   数据帧校验: ${securityConfig['dataFrameChecksum']}');
    print('   数据帧加密: ${securityConfig['dataFrameEncrypt']}');

    // 7. 测试数据加密
    print('\n6. 测试数据加密...');
    final testData = Uint8List.fromList('Hello ESP32 BluFi!'.codeUnits);
    print('   原始数据: "${String.fromCharCodes(testData)}"');
    print('   原始长度: ${testData.length} 字节');

    final encryptedData = securityManager.encryptData(testData, 1);
    if (encryptedData == null) {
      print('❌ 数据加密失败');
      return;
    }
    print('✅ 数据加密成功');
    print('   加密长度: ${encryptedData.length} 字节');
    print(
        '   加密数据: ${encryptedData.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}');

    // 8. 测试数据解密
    print('\n7. 测试数据解密...');
    final decryptedData = securityManager.decryptData(encryptedData, 1);
    if (decryptedData == null) {
      print('❌ 数据解密失败');
      return;
    }
    print('✅ 数据解密成功');
    print('   解密长度: ${decryptedData.length} 字节');
    print('   解密数据: "${String.fromCharCodes(decryptedData)}"');

    // 9. 验证数据完整性
    print('\n8. 验证数据完整性...');
    final isDataIntact = _compareUint8Lists(testData, decryptedData);
    if (isDataIntact) {
      print('✅ 数据完整性验证通过');
    } else {
      print('❌ 数据完整性验证失败');
    }

    // 10. 测试校验和功能
    print('\n9. 测试校验和功能...');
    final checksum = securityManager.calculateChecksum(testData, 1);
    if (checksum != null) {
      print('✅ 校验和计算成功');
      print('   校验和: 0x${checksum.toRadixString(16).padLeft(4, '0')}');

      final isChecksumValid =
          securityManager.verifyChecksum(testData, 1, checksum);
      print('   校验验证: ${isChecksumValid ? '✅ 通过' : '❌ 失败'}');
    } else {
      print('ℹ️ 校验和功能已禁用');
    }

    // 11. 显示安全配置摘要
    print('\n10. 安全配置摘要:');
    final summary = securityManager.getSecuritySummary();
    summary.forEach((key, value) {
      print('    $key: $value');
    });

    print('\n=== 示例完成 ===');
    print('✅ 所有加密功能测试通过！');
  } catch (e) {
    print('❌ 发生错误: $e');
  } finally {
    // 清理资源
    securityManager.dispose();
  }
}

/// 比较两个Uint8List是否相等
bool _compareUint8Lists(Uint8List a, Uint8List b) {
  if (a.length != b.length) return false;
  for (int i = 0; i < a.length; i++) {
    if (a[i] != b[i]) return false;
  }
  return true;
}
