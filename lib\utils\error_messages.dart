/// BluFi错误消息本地化工具
class BluFiErrorMessages {
  /// 根据错误代码获取中文错误消息
  static String getErrorMessage(int errorCode) {
    switch (errorCode) {
      case 0x0: // errorSequence
        return '序列号错误 - 数据包顺序不正确，可能存在网络问题';
      case 0x1: // errorChecksum
        return '校验和错误 - 数据传输过程中出现损坏，请重试';
      case 0x2: // errorDecrypt
        return '解密错误 - 无法解密接收到的数据，请检查加密设置';
      case 0x3: // errorEncrypt
        return '加密错误 - 无法加密要发送的数据，请检查加密设置';
      case 0x4: // errorInitSecurity
        return '安全初始化错误 - 无法建立安全连接，请重新连接设备';
      case 0x5: // errorDhMalloc
        return 'DH内存分配错误 - 设备内存不足，请重启设备后重试';
      case 0x6: // errorDhParam
        return 'DH参数错误 - 密钥协商参数不匹配，请检查设备兼容性';
      case 0x7: // errorReadParam
        return '参数读取错误 - 无法读取设备参数，请检查设备状态';
      case 0x8: // errorMakePublic
        return '公钥生成错误 - 无法生成加密公钥，请重新建立连接';
      case 0x9: // errorDataFormat
        return '数据格式错误 - 发送的数据格式不正确，请检查配置';
      case 0xa: // errorCalculateMd5
        return 'MD5计算错误 - 数据完整性验证失败，请重试';
      case 0xb: // errorWifiScan
        return 'WiFi扫描错误 - 无法扫描周围的WiFi网络，请检查设备WiFi功能';
      case 0xc: // errorMsgState
        return '消息状态错误 - 设备当前状态不支持此操作，请稍后重试';
      default:
        return '未知错误 (代码: 0x${errorCode.toRadixString(16)}) - 请联系技术支持';
    }
  }

  /// 根据错误代码获取简短的错误描述
  static String getShortErrorMessage(int errorCode) {
    switch (errorCode) {
      case 0x0:
        return '序列号错误';
      case 0x1:
        return '校验和错误';
      case 0x2:
        return '解密错误';
      case 0x3:
        return '加密错误';
      case 0x4:
        return '安全初始化错误';
      case 0x5:
        return 'DH内存分配错误';
      case 0x6:
        return 'DH参数错误';
      case 0x7:
        return '参数读取错误';
      case 0x8:
        return '公钥生成错误';
      case 0x9:
        return '数据格式错误';
      case 0xa:
        return 'MD5计算错误';
      case 0xb:
        return 'WiFi扫描错误';
      case 0xc:
        return '消息状态错误';
      default:
        return '未知错误 (0x${errorCode.toRadixString(16)})';
    }
  }

  /// 获取错误的严重程度
  static ErrorSeverity getErrorSeverity(int errorCode) {
    switch (errorCode) {
      case 0x0: // 序列号错误
      case 0x1: // 校验和错误
      case 0xa: // MD5计算错误
        return ErrorSeverity.warning; // 可以重试的错误
      
      case 0x2: // 解密错误
      case 0x3: // 加密错误
      case 0x4: // 安全初始化错误
      case 0x6: // DH参数错误
      case 0x8: // 公钥生成错误
        return ErrorSeverity.critical; // 需要重新建立连接的错误
      
      case 0x5: // DH内存分配错误
        return ErrorSeverity.fatal; // 需要重启设备的错误
      
      case 0x7: // 参数读取错误
      case 0x9: // 数据格式错误
      case 0xb: // WiFi扫描错误
      case 0xc: // 消息状态错误
        return ErrorSeverity.recoverable; // 可以通过重试或调整操作恢复的错误
      
      default:
        return ErrorSeverity.unknown;
    }
  }

  /// 获取错误的建议操作
  static List<String> getErrorSuggestions(int errorCode) {
    switch (errorCode) {
      case 0x0: // 序列号错误
        return ['检查网络连接稳定性', '重新发送数据', '如果持续出现请重新连接设备'];
      
      case 0x1: // 校验和错误
      case 0xa: // MD5计算错误
        return ['检查蓝牙连接质量', '重新发送数据', '尝试靠近设备'];
      
      case 0x2: // 解密错误
      case 0x3: // 加密错误
        return ['重新建立安全连接', '检查设备兼容性', '确认设备支持当前加密方式'];
      
      case 0x4: // 安全初始化错误
      case 0x6: // DH参数错误
      case 0x8: // 公钥生成错误
        return ['断开并重新连接设备', '检查设备固件版本', '确认设备支持BluFi协议'];
      
      case 0x5: // DH内存分配错误
        return ['重启ESP32设备', '检查设备内存使用情况', '尝试减少并发连接'];
      
      case 0x7: // 参数读取错误
        return ['检查设备状态', '等待设备就绪后重试', '确认设备配置正确'];
      
      case 0x9: // 数据格式错误
        return ['检查WiFi配置参数', '确认SSID和密码格式正确', '检查特殊字符使用'];
      
      case 0xb: // WiFi扫描错误
        return ['检查设备WiFi功能', '确认设备天线连接正常', '尝试在不同位置扫描'];
      
      case 0xc: // 消息状态错误
        return ['等待设备完成当前操作', '检查设备状态', '稍后重试'];
      
      default:
        return ['记录错误信息', '重新连接设备', '联系技术支持'];
    }
  }
}

/// 错误严重程度枚举
enum ErrorSeverity {
  warning,     // 警告 - 黄色
  recoverable, // 可恢复 - 橙色
  critical,    // 严重 - 红色
  fatal,       // 致命 - 深红色
  unknown,     // 未知 - 灰色
}

/// 错误严重程度扩展
extension ErrorSeverityExtension on ErrorSeverity {
  /// 获取对应的颜色
  int get colorValue {
    switch (this) {
      case ErrorSeverity.warning:
        return 0xFFFFC107; // 黄色
      case ErrorSeverity.recoverable:
        return 0xFFFF9800; // 橙色
      case ErrorSeverity.critical:
        return 0xFFF44336; // 红色
      case ErrorSeverity.fatal:
        return 0xFF8B0000; // 深红色
      case ErrorSeverity.unknown:
        return 0xFF9E9E9E; // 灰色
    }
  }

  /// 获取严重程度描述
  String get description {
    switch (this) {
      case ErrorSeverity.warning:
        return '警告';
      case ErrorSeverity.recoverable:
        return '可恢复';
      case ErrorSeverity.critical:
        return '严重';
      case ErrorSeverity.fatal:
        return '致命';
      case ErrorSeverity.unknown:
        return '未知';
    }
  }
}
