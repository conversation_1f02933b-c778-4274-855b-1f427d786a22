import 'dart:typed_data';
import '../utils/blufi_constants.dart';
import '../utils/extensions.dart';

/// BluFi协议帧模型
class BluFiFrame {
  /// 帧类型（控制帧或数据帧）
  final int type;

  /// 帧子类型
  final int subtype;

  /// 帧控制字段
  final int frameControl;

  /// 序列号
  final int sequence;

  /// 数据内容
  final Uint8List data;

  /// 校验和
  final int? checksum;

  const BluFiFrame({
    required this.type,
    required this.subtype,
    required this.frameControl,
    required this.sequence,
    required this.data,
    this.checksum,
  });

  /// 创建控制帧
  factory BluFiFrame.control({
    required int subtype,
    required int sequence,
    Uint8List? data,
    bool requireAck = false,
    bool encrypted = false,
    bool hasChecksum = false,
  }) {
    int frameControl = 0;
    if (encrypted) frameControl |= BluFiConstants.frameCtrlEncrypted;
    if (hasChecksum) frameControl |= BluFiConstants.frameCtrlChecksum;
    if (requireAck) frameControl |= BluFiConstants.frameCtrlRequireAck;

    return BluFiFrame(
      type: BluFiConstants.frameTypeControl,
      subtype: subtype,
      frameControl: frameControl,
      sequence: sequence,
      data: data ?? Uint8List(0),
    );
  }

  /// 创建数据帧
  factory BluFiFrame.data({
    required int subtype,
    required int sequence,
    required Uint8List data,
    bool requireAck = false,
    bool encrypted = false,
    bool hasChecksum = false,
    bool isFragment = false,
  }) {
    int frameControl = 0;
    if (encrypted) frameControl |= BluFiConstants.frameCtrlEncrypted;
    if (hasChecksum) frameControl |= BluFiConstants.frameCtrlChecksum;
    if (requireAck) frameControl |= BluFiConstants.frameCtrlRequireAck;
    if (isFragment) frameControl |= BluFiConstants.frameCtrlFrag;

    return BluFiFrame(
      type: BluFiConstants.frameTypeData,
      subtype: subtype,
      frameControl: frameControl,
      sequence: sequence,
      data: data,
    );
  }

  /// 创建ACK帧
  factory BluFiFrame.ack({
    required int sequence,
    required int ackedSequence,
  }) {
    final ackData = ackedSequence.toUint16LE();
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeAck,
      sequence: sequence,
      data: ackData,
    );
  }

  /// 从字节数组解析帧
  factory BluFiFrame.fromBytes(Uint8List bytes) {
    if (bytes.length < 4) {
      throw FormatException('Frame too short: ${bytes.length} bytes');
    }

    final int typeAndSubtype = bytes[0];
    final int type = typeAndSubtype & 0x03;
    final int subtype = (typeAndSubtype >> 2) & 0x3F;
    final int frameControl = bytes[1];
    final int sequence = bytes[2];
    final int dataLength = bytes[3];

    if (bytes.length < 4 + dataLength) {
      throw const FormatException('Invalid frame length');
    }

    final Uint8List data = bytes.sublist(4, 4 + dataLength);

    int? checksum;
    if ((frameControl & BluFiConstants.frameCtrlChecksum) != 0) {
      if (bytes.length < 4 + dataLength + 2) {
        throw const FormatException('Missing checksum');
      }
      checksum = bytes.readUint16LE(4 + dataLength);
    }

    return BluFiFrame(
      type: type,
      subtype: subtype,
      frameControl: frameControl,
      sequence: sequence,
      data: data,
      checksum: checksum,
    );
  }

  /// 转换为字节数组
  Uint8List toBytes() {
    final List<int> bytes = [];

    // Type and Subtype
    bytes.add((subtype << 2) | type);

    // Frame Control
    bytes.add(frameControl);

    // Sequence
    bytes.add(sequence);

    // Data Length
    bytes.add(data.length);

    // Data
    bytes.addAll(data);

    // Checksum (if required)
    if (hasChecksum) {
      final checksumData =
          Uint8List.fromList([sequence, data.length] + data.toList());
      final calculatedChecksum = checksumData.calculateCrc16();
      bytes.addAll(calculatedChecksum.toUint16LE());
    }

    return Uint8List.fromList(bytes);
  }

  /// 检查是否为控制帧
  bool get isControlFrame => type == BluFiConstants.frameTypeControl;

  /// 检查是否为数据帧
  bool get isDataFrame => type == BluFiConstants.frameTypeData;

  /// 检查是否加密
  bool get isEncrypted =>
      (frameControl & BluFiConstants.frameCtrlEncrypted) != 0;

  /// 检查是否包含校验和
  bool get hasChecksum =>
      (frameControl & BluFiConstants.frameCtrlChecksum) != 0;

  /// 检查数据方向
  bool get isFromDevice =>
      (frameControl & BluFiConstants.frameCtrlDirection) != 0;

  /// 检查是否需要ACK
  bool get requiresAck =>
      (frameControl & BluFiConstants.frameCtrlRequireAck) != 0;

  /// 检查是否为分片帧
  bool get isFragment => (frameControl & BluFiConstants.frameCtrlFrag) != 0;

  /// 获取帧类型描述
  String get typeDescription {
    if (isControlFrame) {
      switch (subtype) {
        case BluFiConstants.ctrlSubtypeAck:
          return 'ACK';
        case BluFiConstants.ctrlSubtypeSetSecurityMode:
          return '设置安全模式';
        case BluFiConstants.ctrlSubtypeSetOpMode:
          return '设置操作模式';
        case BluFiConstants.ctrlSubtypeConnectWifi:
          return '连接WiFi';
        case BluFiConstants.ctrlSubtypeDisconnectWifi:
          return '断开WiFi';
        case BluFiConstants.ctrlSubtypeGetWifiStatus:
          return '获取WiFi状态';
        case BluFiConstants.ctrlSubtypeGetVersion:
          return '获取版本';
        case BluFiConstants.ctrlSubtypeGetWifiList:
          return '获取WiFi列表';
        default:
          return '控制帧($subtype)';
      }
    } else {
      switch (subtype) {
        case BluFiConstants.dataSubtypeNegotiateData:
          return '协商数据';
        case BluFiConstants.dataSubtypeStaSsid:
          return 'STA SSID';
        case BluFiConstants.dataSubtypeStaPassword:
          return 'STA密码';
        case BluFiConstants.dataSubtypeSoftapSsid:
          return 'AP SSID';
        case BluFiConstants.dataSubtypeSoftapPassword:
          return 'AP密码';
        case BluFiConstants.dataSubtypeWifiConnectionState:
          return 'WiFi状态';
        case BluFiConstants.dataSubtypeVersion:
          return '版本信息';
        case BluFiConstants.dataSubtypeWifiList:
          return 'WiFi列表';
        case BluFiConstants.dataSubtypeError:
          return '错误报告';
        default:
          return '数据帧($subtype)';
      }
    }
  }

  /// 验证校验和
  bool validateChecksum() {
    if (!hasChecksum || checksum == null) return true;

    final checksumData =
        Uint8List.fromList([sequence, data.length] + data.toList());
    final calculatedChecksum = checksumData.calculateCrc16();
    return calculatedChecksum == checksum;
  }

  @override
  String toString() {
    return 'BluFiFrame{type: $typeDescription, sequence: $sequence, '
        'dataLength: ${data.length}, encrypted: $isEncrypted, '
        'hasChecksum: $hasChecksum, requiresAck: $requiresAck}';
  }

  /// 转换为JSON（用于调试）
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'subtype': subtype,
      'typeDescription': typeDescription,
      'frameControl': frameControl,
      'sequence': sequence,
      'dataLength': data.length,
      'data': data.toHexString(),
      'checksum': checksum,
      'isEncrypted': isEncrypted,
      'hasChecksum': hasChecksum,
      'isFromDevice': isFromDevice,
      'requiresAck': requiresAck,
      'isFragment': isFragment,
    };
  }
}
