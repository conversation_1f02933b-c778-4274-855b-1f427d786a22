import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:logger/logger.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// 权限管理服务
/// 负责处理应用所需的各种权限
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  final Logger _logger = Logger();

  /// 检查蓝牙权限状态
  Future<bool> checkBluetoothPermissions() async {
    try {
      _logger.i('Checking Bluetooth permissions');

      // 根据平台检查不同的权限
      if (Platform.isAndroid) {
        return await _checkAndroidBluetoothPermissions();
      } else if (Platform.isIOS) {
        return await _checkIOSBluetoothPermissions();
      }

      return false;
    } catch (e) {
      _logger.e('Error checking Bluetooth permissions: $e');
      return false;
    }
  }

  /// 请求蓝牙权限
  Future<bool> requestBluetoothPermissions() async {
    try {
      _logger.i('Requesting Bluetooth permissions');

      if (Platform.isAndroid) {
        return await _requestAndroidBluetoothPermissions();
      } else if (Platform.isIOS) {
        return await _requestIOSBluetoothPermissions();
      }

      return false;
    } catch (e) {
      _logger.e('Error requesting Bluetooth permissions: $e');
      return false;
    }
  }

  /// 检查Android蓝牙权限
  Future<bool> _checkAndroidBluetoothPermissions() async {
    try {
      // Android 12 (API 31) 及以上版本需要的权限
      final androidVersion = await _getAndroidVersion();
      _logger.i('Android version: $androidVersion');

      List<Permission> requiredPermissions;

      if (androidVersion >= 31) {
        // Android 12+
        requiredPermissions = [
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.bluetoothAdvertise,
        ];

        // 对于某些设备，可能仍需要位置权限
        if (androidVersion < 33) {
          requiredPermissions.add(Permission.locationWhenInUse);
        }
      } else {
        // Android 11 及以下
        requiredPermissions = [
          Permission.bluetooth,
          Permission.location,
          Permission.locationWhenInUse,
        ];
      }

      // 检查所有必需权限
      for (final permission in requiredPermissions) {
        final status = await permission.status;
        _logger.d('Permission $permission status: $status');

        if (status != PermissionStatus.granted) {
          _logger.w('Permission not granted: $permission (status: $status)');
          return false;
        }
      }

      _logger.i('All Android Bluetooth permissions granted');
      return true;
    } catch (e) {
      _logger.e('Error checking Android Bluetooth permissions: $e');
      return false;
    }
  }

  /// 检查iOS蓝牙权限
  Future<bool> _checkIOSBluetoothPermissions() async {
    final bluetoothStatus = await Permission.bluetooth.status;

    if (bluetoothStatus == PermissionStatus.granted) {
      _logger.i('iOS Bluetooth permission granted');
      return true;
    }

    _logger.w('iOS Bluetooth permission not granted: $bluetoothStatus');
    return false;
  }

  /// 请求Android蓝牙权限
  Future<bool> _requestAndroidBluetoothPermissions() async {
    try {
      final androidVersion = await _getAndroidVersion();
      _logger.i('Requesting permissions for Android version: $androidVersion');

      List<Permission> requiredPermissions;

      if (androidVersion >= 31) {
        // Android 12+
        requiredPermissions = [
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.bluetoothAdvertise,
        ];

        // 对于某些设备，可能仍需要位置权限
        if (androidVersion < 33) {
          requiredPermissions.add(Permission.locationWhenInUse);
        }
      } else {
        // Android 11 及以下
        requiredPermissions = [
          Permission.bluetooth,
          Permission.location,
          Permission.locationWhenInUse,
        ];
      }

      // 分别请求权限以获得更好的用户体验
      final Map<Permission, PermissionStatus> statuses = {};

      for (final permission in requiredPermissions) {
        // 检查是否需要显示权限说明
        if (await permission.shouldShowRequestRationale) {
          _logger.i('Should show rationale for permission: $permission');
        }

        final status = await permission.request();
        statuses[permission] = status;
        _logger.d('Permission $permission requested, status: $status');
      }

      // 检查结果
      final List<Permission> deniedPermissions = [];
      final List<Permission> permanentlyDeniedPermissions = [];

      for (final permission in requiredPermissions) {
        final status = statuses[permission];

        if (status == PermissionStatus.permanentlyDenied) {
          permanentlyDeniedPermissions.add(permission);
          _logger.e('Permission permanently denied: $permission');
        } else if (status != PermissionStatus.granted) {
          deniedPermissions.add(permission);
          _logger.w('Permission denied: $permission (status: $status)');
        }
      }

      if (permanentlyDeniedPermissions.isNotEmpty) {
        _logger.e(
            'Some permissions permanently denied: $permanentlyDeniedPermissions');
        return false;
      }

      if (deniedPermissions.isNotEmpty) {
        _logger.w('Some permissions denied: $deniedPermissions');
        return false;
      }

      _logger.i('All Android Bluetooth permissions granted');
      return true;
    } catch (e) {
      _logger.e('Error requesting Android Bluetooth permissions: $e');
      return false;
    }
  }

  /// 请求iOS蓝牙权限
  Future<bool> _requestIOSBluetoothPermissions() async {
    final status = await Permission.bluetooth.request();

    if (status == PermissionStatus.granted) {
      _logger.i('iOS Bluetooth permission granted');
      return true;
    }

    _logger.w('iOS Bluetooth permission denied: $status');
    return false;
  }

  /// 获取Android版本
  Future<int> _getAndroidVersion() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.version.sdkInt;
      }
      return 0;
    } catch (e) {
      _logger.w('Failed to get Android version: $e');
      // 返回一个安全的默认值 (Android 12)
      return 31;
    }
  }

  /// 检查权限是否被永久拒绝
  Future<bool> isPermissionPermanentlyDenied(Permission permission) async {
    final status = await permission.status;
    return status == PermissionStatus.permanentlyDenied;
  }

  /// 打开应用设置页面
  Future<bool> openAppSettings() async {
    try {
      _logger.i('Opening app settings');
      return await openAppSettings();
    } catch (e) {
      _logger.e('Error opening app settings: $e');
      return false;
    }
  }

  /// 检查位置服务是否启用
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Permission.locationWhenInUse.serviceStatus.isEnabled;
    } catch (e) {
      _logger.e('Error checking location service: $e');
      return false;
    }
  }

  /// 获取权限状态描述
  String getPermissionStatusDescription(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return '已授权';
      case PermissionStatus.denied:
        return '被拒绝';
      case PermissionStatus.restricted:
        return '受限制';
      case PermissionStatus.limited:
        return '有限授权';
      case PermissionStatus.permanentlyDenied:
        return '永久拒绝';
      default:
        return '未知状态';
    }
  }

  /// 获取详细的权限状态信息
  Future<Map<String, String>> getDetailedPermissionStatus() async {
    final result = <String, String>{};

    try {
      if (Platform.isAndroid) {
        final androidVersion = await _getAndroidVersion();

        if (androidVersion >= 31) {
          // Android 12+
          result['蓝牙扫描'] = getPermissionStatusDescription(
            await Permission.bluetoothScan.status,
          );
          result['蓝牙连接'] = getPermissionStatusDescription(
            await Permission.bluetoothConnect.status,
          );
          result['蓝牙广播'] = getPermissionStatusDescription(
            await Permission.bluetoothAdvertise.status,
          );
        } else {
          // Android 11 及以下
          result['蓝牙'] = getPermissionStatusDescription(
            await Permission.bluetooth.status,
          );
        }

        result['位置信息'] = getPermissionStatusDescription(
          await Permission.locationWhenInUse.status,
        );
      } else if (Platform.isIOS) {
        result['蓝牙'] = getPermissionStatusDescription(
          await Permission.bluetooth.status,
        );
      }
    } catch (e) {
      _logger.e('Error getting detailed permission status: $e');
      result['错误'] = '无法获取权限状态';
    }

    return result;
  }

  /// 检查是否需要显示权限说明
  Future<bool> shouldShowPermissionRationale(Permission permission) async {
    if (Platform.isAndroid) {
      return await permission.shouldShowRequestRationale;
    }
    return false;
  }

  /// 获取被永久拒绝的权限列表
  Future<List<Permission>> getPermanentlyDeniedPermissions() async {
    final List<Permission> permanentlyDenied = [];

    try {
      final androidVersion = await _getAndroidVersion();
      List<Permission> allPermissions;

      if (Platform.isAndroid) {
        if (androidVersion >= 31) {
          allPermissions = [
            Permission.bluetoothScan,
            Permission.bluetoothConnect,
            Permission.bluetoothAdvertise,
            Permission.locationWhenInUse,
          ];
        } else {
          allPermissions = [
            Permission.bluetooth,
            Permission.location,
            Permission.locationWhenInUse,
          ];
        }
      } else if (Platform.isIOS) {
        allPermissions = [Permission.bluetooth];
      } else {
        return permanentlyDenied;
      }

      for (final permission in allPermissions) {
        final status = await permission.status;
        if (status == PermissionStatus.permanentlyDenied) {
          permanentlyDenied.add(permission);
        }
      }
    } catch (e) {
      _logger.e('Error getting permanently denied permissions: $e');
    }

    return permanentlyDenied;
  }

  /// 检查是否有权限被永久拒绝
  Future<bool> hasPermissionsPermanentlyDenied() async {
    final permanentlyDenied = await getPermanentlyDeniedPermissions();
    return permanentlyDenied.isNotEmpty;
  }

  /// 获取权限请求的用户友好说明
  String getPermissionDescription(Permission permission) {
    switch (permission) {
      case Permission.bluetooth:
        return '蓝牙权限用于连接和通信ESP32设备';
      case Permission.bluetoothScan:
        return '蓝牙扫描权限用于发现附近的ESP32设备';
      case Permission.bluetoothConnect:
        return '蓝牙连接权限用于与ESP32设备建立连接';
      case Permission.bluetoothAdvertise:
        return '蓝牙广播权限用于设备间的通信';
      case Permission.location:
      case Permission.locationWhenInUse:
        return '位置权限在某些Android设备上是蓝牙扫描所必需的';
      default:
        return '此权限是应用正常运行所必需的';
    }
  }

  /// 重新检查所有权限状态
  Future<bool> recheckAllPermissions() async {
    _logger.i('Rechecking all permissions');
    return await checkBluetoothPermissions();
  }
}
