import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/permission_service.dart';

/// 权限帮助工具类
/// 提供权限相关的UI交互和处理逻辑
class PermissionHelper {
  static final PermissionService _permissionService = PermissionService();

  /// 显示权限请求对话框
  static Future<bool> showPermissionDialog(
    BuildContext context, {
    String? title,
    String? message,
    List<Permission>? permissions,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title ?? '权限请求'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message ?? '应用需要以下权限才能正常工作：'),
              const SizedBox(height: 16),
              if (permissions != null)
                ...permissions.map((permission) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle_outline, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _permissionService
                                  .getPermissionDescription(permission),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    )),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('授权'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// 显示权限被永久拒绝的对话框
  static Future<bool> showPermanentlyDeniedDialog(
    BuildContext context, {
    List<Permission>? deniedPermissions,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('权限被拒绝'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('以下权限被永久拒绝，请在系统设置中手动开启：'),
              const SizedBox(height: 16),
              if (deniedPermissions != null)
                ...deniedPermissions.map((permission) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          const Icon(Icons.warning,
                              size: 16, color: Colors.orange),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _permissionService
                                  .getPermissionDescription(permission),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    )),
              const SizedBox(height: 16),
              const Text(
                '点击"打开设置"跳转到应用权限设置页面',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('打开设置'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// 检查并请求蓝牙权限的完整流程
  static Future<bool> checkAndRequestBluetoothPermissions(
    BuildContext context, {
    bool showDialog = true,
  }) async {
    try {
      // 首先检查权限状态
      final hasPermissions =
          await _permissionService.checkBluetoothPermissions();
      if (hasPermissions) {
        return true;
      }

      // 检查是否有权限被永久拒绝
      final permanentlyDenied =
          await _permissionService.getPermanentlyDeniedPermissions();
      if (permanentlyDenied.isNotEmpty) {
        if (showDialog && context.mounted) {
          final shouldOpenSettings = await showPermanentlyDeniedDialog(
            context,
            deniedPermissions: permanentlyDenied,
          );

          if (shouldOpenSettings) {
            await _permissionService.openAppSettings();
          }
        }
        return false;
      }

      // 显示权限请求对话框
      if (showDialog) {
        final requiredPermissions = await _getRequiredPermissions();
        if (context.mounted) {
          final shouldRequest = await showPermissionDialog(
            context,
            title: '蓝牙权限请求',
            message: '应用需要蓝牙权限来连接ESP32设备：',
            permissions: requiredPermissions,
          );

          if (!shouldRequest) {
            return false;
          }
        }
      }

      // 请求权限
      final granted = await _permissionService.requestBluetoothPermissions();
      return granted;
    } catch (e) {
      debugPrint('Error in permission flow: $e');
      return false;
    }
  }

  /// 获取当前平台所需的权限列表
  static Future<List<Permission>> _getRequiredPermissions() async {
    if (Platform.isAndroid) {
      // 简化版本，实际应该从 PermissionService 获取
      return [
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.bluetoothAdvertise,
        Permission.locationWhenInUse,
      ];
    } else if (Platform.isIOS) {
      return [Permission.bluetooth];
    }
    return [];
  }

  /// 显示权限状态信息
  static Future<void> showPermissionStatus(BuildContext context) async {
    final status = await _permissionService.getDetailedPermissionStatus();

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('权限状态'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: status.entries.map((entry) {
              final isGranted = entry.value == '已授权';
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      isGranted ? Icons.check_circle : Icons.cancel,
                      size: 16,
                      color: isGranted ? Colors.green : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text('${entry.key}: ${entry.value}'),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
