import '../utils/blufi_constants.dart';

/// WiFi配置模型
class WiFiConfig {
  /// WiFi操作模式
  final int opMode;
  
  /// Station模式配置
  final StationConfig? stationConfig;
  
  /// SoftAP模式配置
  final SoftApConfig? softApConfig;

  const WiFiConfig({
    required this.opMode,
    this.stationConfig,
    this.softApConfig,
  });

  /// 创建Station模式配置
  factory WiFiConfig.station({
    required String ssid,
    required String password,
    String? bssid,
  }) {
    return WiFiConfig(
      opMode: BluFiConstants.wifiModeSta,
      stationConfig: StationConfig(
        ssid: ssid,
        password: password,
        bssid: bssid,
      ),
    );
  }

  /// 创建SoftAP模式配置
  factory WiFiConfig.softAp({
    required String ssid,
    required String password,
    int authMode = BluFiConstants.wifiAuthWpa2Psk,
    int channel = 1,
    int maxConnections = 4,
  }) {
    return WiFiConfig(
      opMode: BluFiConstants.wifiModeAp,
      softApConfig: SoftApConfig(
        ssid: ssid,
        password: password,
        authMode: authMode,
        channel: channel,
        maxConnections: maxConnections,
      ),
    );
  }

  /// 创建Station+SoftAP模式配置
  factory WiFiConfig.stationSoftAp({
    required StationConfig stationConfig,
    required SoftApConfig softApConfig,
  }) {
    return WiFiConfig(
      opMode: BluFiConstants.wifiModeApSta,
      stationConfig: stationConfig,
      softApConfig: softApConfig,
    );
  }

  /// 获取模式描述
  String get modeDescription {
    switch (opMode) {
      case BluFiConstants.wifiModeSta:
        return 'Station模式';
      case BluFiConstants.wifiModeAp:
        return 'SoftAP模式';
      case BluFiConstants.wifiModeApSta:
        return 'Station+SoftAP模式';
      default:
        return '未知模式';
    }
  }

  /// 验证配置是否有效
  bool get isValid {
    switch (opMode) {
      case BluFiConstants.wifiModeSta:
        return stationConfig?.isValid ?? false;
      case BluFiConstants.wifiModeAp:
        return softApConfig?.isValid ?? false;
      case BluFiConstants.wifiModeApSta:
        return (stationConfig?.isValid ?? false) && (softApConfig?.isValid ?? false);
      default:
        return false;
    }
  }

  @override
  String toString() {
    return 'WiFiConfig{opMode: $opMode, stationConfig: $stationConfig, softApConfig: $softApConfig}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'opMode': opMode,
      'stationConfig': stationConfig?.toJson(),
      'softApConfig': softApConfig?.toJson(),
    };
  }

  /// 从JSON创建
  factory WiFiConfig.fromJson(Map<String, dynamic> json) {
    return WiFiConfig(
      opMode: json['opMode'] ?? BluFiConstants.wifiModeNull,
      stationConfig: json['stationConfig'] != null 
          ? StationConfig.fromJson(json['stationConfig']) 
          : null,
      softApConfig: json['softApConfig'] != null 
          ? SoftApConfig.fromJson(json['softApConfig']) 
          : null,
    );
  }
}

/// Station模式配置
class StationConfig {
  /// WiFi网络SSID
  final String ssid;
  
  /// WiFi密码
  final String password;
  
  /// BSSID（可选，用于指定特定的接入点）
  final String? bssid;

  const StationConfig({
    required this.ssid,
    required this.password,
    this.bssid,
  });

  /// 验证配置是否有效
  bool get isValid {
    return ssid.isNotEmpty && password.isNotEmpty;
  }

  @override
  String toString() {
    return 'StationConfig{ssid: $ssid, password: [HIDDEN], bssid: $bssid}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'ssid': ssid,
      'password': password,
      'bssid': bssid,
    };
  }

  /// 从JSON创建
  factory StationConfig.fromJson(Map<String, dynamic> json) {
    return StationConfig(
      ssid: json['ssid'] ?? '',
      password: json['password'] ?? '',
      bssid: json['bssid'],
    );
  }
}

/// SoftAP模式配置
class SoftApConfig {
  /// AP的SSID
  final String ssid;
  
  /// AP的密码
  final String password;
  
  /// 认证模式
  final int authMode;
  
  /// 信道
  final int channel;
  
  /// 最大连接数
  final int maxConnections;

  const SoftApConfig({
    required this.ssid,
    required this.password,
    this.authMode = BluFiConstants.wifiAuthWpa2Psk,
    this.channel = 1,
    this.maxConnections = 4,
  });

  /// 验证配置是否有效
  bool get isValid {
    return ssid.isNotEmpty && 
           password.isNotEmpty && 
           channel >= 1 && 
           channel <= 14 && 
           maxConnections >= 1 && 
           maxConnections <= 10;
  }

  /// 获取认证模式描述
  String get authModeDescription {
    switch (authMode) {
      case BluFiConstants.wifiAuthOpen:
        return '开放';
      case BluFiConstants.wifiAuthWep:
        return 'WEP';
      case BluFiConstants.wifiAuthWpaPsk:
        return 'WPA PSK';
      case BluFiConstants.wifiAuthWpa2Psk:
        return 'WPA2 PSK';
      case BluFiConstants.wifiAuthWpaWpa2Psk:
        return 'WPA/WPA2 PSK';
      default:
        return '未知';
    }
  }

  @override
  String toString() {
    return 'SoftApConfig{ssid: $ssid, password: [HIDDEN], authMode: $authMode, channel: $channel, maxConnections: $maxConnections}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'ssid': ssid,
      'password': password,
      'authMode': authMode,
      'channel': channel,
      'maxConnections': maxConnections,
    };
  }

  /// 从JSON创建
  factory SoftApConfig.fromJson(Map<String, dynamic> json) {
    return SoftApConfig(
      ssid: json['ssid'] ?? '',
      password: json['password'] ?? '',
      authMode: json['authMode'] ?? BluFiConstants.wifiAuthWpa2Psk,
      channel: json['channel'] ?? 1,
      maxConnections: json['maxConnections'] ?? 4,
    );
  }
}
