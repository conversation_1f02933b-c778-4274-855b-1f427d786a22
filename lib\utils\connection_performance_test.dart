import 'dart:async';
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../services/bluetooth_service.dart';
import 'connection_cache.dart';

/// 连接性能测试工具
/// 用于测试和验证蓝牙连接优化效果
class ConnectionPerformanceTest {
  static final Logger _logger = Logger();
  
  /// 执行连接性能测试
  static Future<ConnectionTestResult> runConnectionTest(
    BluetoothService bluetoothService,
    BluFiDevice device, {
    int testRounds = 5,
    Duration delayBetweenTests = const Duration(seconds: 2),
  }) async {
    _logger.i('Starting connection performance test for device: ${device.name}');
    
    final results = <ConnectionAttemptResult>[];
    final deviceId = device.bluetoothDevice.remoteId.toString();
    
    // 重置设备缓存以获得准确的测试结果
    ConnectionStateCache.resetDevice(deviceId);
    
    for (int round = 1; round <= testRounds; round++) {
      _logger.i('Test round $round/$testRounds');
      
      final attemptResult = await _performSingleConnectionTest(
        bluetoothService,
        device,
        round,
      );
      
      results.add(attemptResult);
      
      // 如果不是最后一轮，等待一段时间再进行下一次测试
      if (round < testRounds) {
        await Future.delayed(delayBetweenTests);
      }
    }
    
    final testResult = ConnectionTestResult(
      deviceId: deviceId,
      deviceName: device.name,
      testRounds: testRounds,
      results: results,
    );
    
    _logger.i('Connection performance test completed');
    _logTestSummary(testResult);
    
    return testResult;
  }
  
  /// 执行单次连接测试
  static Future<ConnectionAttemptResult> _performSingleConnectionTest(
    BluetoothService bluetoothService,
    BluFiDevice device,
    int roundNumber,
  ) async {
    final startTime = DateTime.now();
    bool success = false;
    String? errorMessage;
    
    try {
      // 确保从断开状态开始
      if (bluetoothService.connectionState != BluetoothConnectionState.disconnected) {
        await bluetoothService.disconnect();
        await Future.delayed(const Duration(milliseconds: 500));
      }
      
      // 尝试连接
      success = await bluetoothService.connectToDevice(device);
      
      if (!success) {
        errorMessage = 'Connection returned false';
      }
      
    } catch (e) {
      success = false;
      errorMessage = e.toString();
    }
    
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    final result = ConnectionAttemptResult(
      roundNumber: roundNumber,
      success: success,
      duration: duration,
      errorMessage: errorMessage,
      timestamp: startTime,
    );
    
    _logger.i('Round $roundNumber: ${success ? 'SUCCESS' : 'FAILED'} in ${duration.inMilliseconds}ms');
    
    return result;
  }
  
  /// 记录测试摘要
  static void _logTestSummary(ConnectionTestResult result) {
    final successCount = result.successfulAttempts.length;
    final failureCount = result.failedAttempts.length;
    final successRate = (successCount / result.testRounds * 100).toStringAsFixed(1);
    
    _logger.i('=== Connection Performance Test Summary ===');
    _logger.i('Device: ${result.deviceName} (${result.deviceId})');
    _logger.i('Test Rounds: ${result.testRounds}');
    _logger.i('Success Rate: $successRate% ($successCount/$result.testRounds)');
    
    if (result.averageConnectionTime != null) {
      _logger.i('Average Connection Time: ${result.averageConnectionTime!.inMilliseconds}ms');
    }
    
    if (result.fastestConnectionTime != null) {
      _logger.i('Fastest Connection: ${result.fastestConnectionTime!.inMilliseconds}ms');
    }
    
    if (result.slowestConnectionTime != null) {
      _logger.i('Slowest Connection: ${result.slowestConnectionTime!.inMilliseconds}ms');
    }
    
    if (result.failedAttempts.isNotEmpty) {
      _logger.w('Failed Attempts:');
      for (final failure in result.failedAttempts) {
        _logger.w('  Round ${failure.roundNumber}: ${failure.errorMessage}');
      }
    }
    
    _logger.i('==========================================');
  }
  
  /// 比较两次测试结果
  static ConnectionTestComparison compareResults(
    ConnectionTestResult beforeOptimization,
    ConnectionTestResult afterOptimization,
  ) {
    return ConnectionTestComparison(
      before: beforeOptimization,
      after: afterOptimization,
    );
  }
}

/// 单次连接尝试结果
class ConnectionAttemptResult {
  final int roundNumber;
  final bool success;
  final Duration duration;
  final String? errorMessage;
  final DateTime timestamp;
  
  ConnectionAttemptResult({
    required this.roundNumber,
    required this.success,
    required this.duration,
    this.errorMessage,
    required this.timestamp,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'roundNumber': roundNumber,
      'success': success,
      'durationMs': duration.inMilliseconds,
      'errorMessage': errorMessage,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// 连接测试结果
class ConnectionTestResult {
  final String deviceId;
  final String deviceName;
  final int testRounds;
  final List<ConnectionAttemptResult> results;
  
  ConnectionTestResult({
    required this.deviceId,
    required this.deviceName,
    required this.testRounds,
    required this.results,
  });
  
  /// 成功的连接尝试
  List<ConnectionAttemptResult> get successfulAttempts =>
      results.where((r) => r.success).toList();
  
  /// 失败的连接尝试
  List<ConnectionAttemptResult> get failedAttempts =>
      results.where((r) => !r.success).toList();
  
  /// 成功率
  double get successRate => successfulAttempts.length / testRounds;
  
  /// 平均连接时间（仅成功的连接）
  Duration? get averageConnectionTime {
    final successful = successfulAttempts;
    if (successful.isEmpty) return null;
    
    final totalMs = successful
        .map((r) => r.duration.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: (totalMs / successful.length).round());
  }
  
  /// 最快连接时间
  Duration? get fastestConnectionTime {
    final successful = successfulAttempts;
    if (successful.isEmpty) return null;
    
    return successful
        .map((r) => r.duration)
        .reduce((a, b) => a.inMilliseconds < b.inMilliseconds ? a : b);
  }
  
  /// 最慢连接时间
  Duration? get slowestConnectionTime {
    final successful = successfulAttempts;
    if (successful.isEmpty) return null;
    
    return successful
        .map((r) => r.duration)
        .reduce((a, b) => a.inMilliseconds > b.inMilliseconds ? a : b);
  }
  
  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'testRounds': testRounds,
      'successRate': successRate,
      'averageConnectionTimeMs': averageConnectionTime?.inMilliseconds,
      'fastestConnectionTimeMs': fastestConnectionTime?.inMilliseconds,
      'slowestConnectionTimeMs': slowestConnectionTime?.inMilliseconds,
      'results': results.map((r) => r.toMap()).toList(),
    };
  }
}

/// 测试结果比较
class ConnectionTestComparison {
  final ConnectionTestResult before;
  final ConnectionTestResult after;
  
  ConnectionTestComparison({
    required this.before,
    required this.after,
  });
  
  /// 成功率改进
  double get successRateImprovement => after.successRate - before.successRate;
  
  /// 平均连接时间改进（毫秒）
  int? get averageTimeImprovement {
    if (before.averageConnectionTime == null || after.averageConnectionTime == null) {
      return null;
    }
    return before.averageConnectionTime!.inMilliseconds - 
           after.averageConnectionTime!.inMilliseconds;
  }
  
  /// 改进百分比
  double? get timeImprovementPercentage {
    final improvement = averageTimeImprovement;
    if (improvement == null || before.averageConnectionTime == null) {
      return null;
    }
    return (improvement / before.averageConnectionTime!.inMilliseconds) * 100;
  }
  
  /// 生成比较报告
  String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== Connection Performance Comparison ===');
    buffer.writeln('Device: ${before.deviceName}');
    buffer.writeln();
    
    buffer.writeln('Success Rate:');
    buffer.writeln('  Before: ${(before.successRate * 100).toStringAsFixed(1)}%');
    buffer.writeln('  After:  ${(after.successRate * 100).toStringAsFixed(1)}%');
    buffer.writeln('  Change: ${(successRateImprovement * 100).toStringAsFixed(1)}%');
    buffer.writeln();
    
    if (before.averageConnectionTime != null && after.averageConnectionTime != null) {
      buffer.writeln('Average Connection Time:');
      buffer.writeln('  Before: ${before.averageConnectionTime!.inMilliseconds}ms');
      buffer.writeln('  After:  ${after.averageConnectionTime!.inMilliseconds}ms');
      if (averageTimeImprovement != null) {
        buffer.writeln('  Improvement: ${averageTimeImprovement}ms (${timeImprovementPercentage?.toStringAsFixed(1)}%)');
      }
    }
    
    buffer.writeln('=========================================');
    return buffer.toString();
  }
}
