import 'package:flutter/material.dart';
import '../utils/blufi_constants.dart';
import '../utils/constants.dart';

/// WiFi配置表单组件
/// 提供WiFi配置的输入界面
class WiFiConfigForm extends StatefulWidget {
  final int selectedMode;
  final String staSsid;
  final String staPassword;
  final String softApSsid;
  final String softApPassword;
  final int softApAuthMode;
  final int softApChannel;
  final int softApMaxConnections;
  final List<String> availableNetworks;
  final ValueChanged<int> onModeChanged;
  final ValueChanged<String> onStaSsidChanged;
  final ValueChanged<String> onStaPasswordChanged;
  final ValueChanged<String> onSoftApSsidChanged;
  final ValueChanged<String> onSoftApPasswordChanged;
  final ValueChanged<int> onSoftApAuthModeChanged;
  final ValueChanged<int> onSoftApChannelChanged;
  final ValueChanged<int> onSoftApMaxConnectionsChanged;

  const WiFiConfigForm({
    Key? key,
    required this.selectedMode,
    required this.staSsid,
    required this.staPassword,
    required this.softApSsid,
    required this.softApPassword,
    required this.softApAuthMode,
    required this.softApChannel,
    required this.softApMaxConnections,
    required this.availableNetworks,
    required this.onModeChanged,
    required this.onStaSsidChanged,
    required this.onStaPasswordChanged,
    required this.onSoftApSsidChanged,
    required this.onSoftApPasswordChanged,
    required this.onSoftApAuthModeChanged,
    required this.onSoftApChannelChanged,
    required this.onSoftApMaxConnectionsChanged,
  }) : super(key: key);

  @override
  State<WiFiConfigForm> createState() => _WiFiConfigFormState();
}

class _WiFiConfigFormState extends State<WiFiConfigForm> {
  late TextEditingController _staSsidController;
  late TextEditingController _staPasswordController;
  late TextEditingController _softApSsidController;
  late TextEditingController _softApPasswordController;

  @override
  void initState() {
    super.initState();
    _staSsidController = TextEditingController(text: widget.staSsid);
    _staPasswordController = TextEditingController(text: widget.staPassword);
    _softApSsidController = TextEditingController(text: widget.softApSsid);
    _softApPasswordController = TextEditingController(text: widget.softApPassword);
  }

  @override
  void dispose() {
    _staSsidController.dispose();
    _staPasswordController.dispose();
    _softApSsidController.dispose();
    _softApPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModeSelector(),
          const SizedBox(height: AppConstants.largePadding),
          if (_shouldShowStationConfig()) ...[
            _buildStationConfig(),
            const SizedBox(height: AppConstants.largePadding),
          ],
          if (_shouldShowSoftApConfig()) ...[
            _buildSoftApConfig(),
          ],
        ],
      ),
    );
  }

  /// 构建模式选择器
  Widget _buildModeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'WiFi模式',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildModeRadioTile(
              BluFiConstants.wifiModeSta,
              'Station模式',
              '连接到现有WiFi网络',
              Icons.wifi,
            ),
            _buildModeRadioTile(
              BluFiConstants.wifiModeAp,
              'SoftAP模式',
              '创建WiFi热点',
              Icons.wifi_tethering,
            ),
            _buildModeRadioTile(
              BluFiConstants.wifiModeApSta,
              'Station+SoftAP模式',
              '同时连接WiFi和创建热点',
              Icons.wifi_tethering_outlined,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建模式单选按钮
  Widget _buildModeRadioTile(int value, String title, String subtitle, IconData icon) {
    return RadioListTile<int>(
      value: value,
      groupValue: widget.selectedMode,
      onChanged: (value) => widget.onModeChanged(value!),
      title: Row(
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
      subtitle: Text(subtitle),
      dense: true,
    );
  }

  /// 构建Station配置
  Widget _buildStationConfig() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Station模式配置',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildNetworkSelector(),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _staSsidController,
              decoration: const InputDecoration(
                labelText: 'WiFi名称(SSID)',
                prefixIcon: Icon(Icons.wifi),
                border: OutlineInputBorder(),
              ),
              onChanged: widget.onStaSsidChanged,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _staPasswordController,
              decoration: const InputDecoration(
                labelText: 'WiFi密码',
                prefixIcon: Icon(Icons.lock),
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              onChanged: widget.onStaPasswordChanged,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建网络选择器
  Widget _buildNetworkSelector() {
    if (widget.availableNetworks.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '可用网络',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            itemCount: widget.availableNetworks.length,
            itemBuilder: (context, index) {
              final network = widget.availableNetworks[index];
              final ssid = network.split(' ').first;
              
              return ListTile(
                dense: true,
                leading: const Icon(Icons.wifi, size: 20),
                title: Text(network),
                onTap: () {
                  _staSsidController.text = ssid;
                  widget.onStaSsidChanged(ssid);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建SoftAP配置
  Widget _buildSoftApConfig() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'SoftAP模式配置',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _softApSsidController,
              decoration: const InputDecoration(
                labelText: '热点名称(SSID)',
                prefixIcon: Icon(Icons.wifi_tethering),
                border: OutlineInputBorder(),
              ),
              onChanged: widget.onSoftApSsidChanged,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _softApPasswordController,
              decoration: const InputDecoration(
                labelText: '热点密码',
                prefixIcon: Icon(Icons.lock),
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              onChanged: widget.onSoftApPasswordChanged,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildAuthModeDropdown(),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(child: _buildChannelDropdown()),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(child: _buildMaxConnectionsDropdown()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建认证模式下拉框
  Widget _buildAuthModeDropdown() {
    return DropdownButtonFormField<int>(
      value: widget.softApAuthMode,
      decoration: const InputDecoration(
        labelText: '安全模式',
        prefixIcon: Icon(Icons.security),
        border: OutlineInputBorder(),
      ),
      items: const [
        DropdownMenuItem(
          value: BluFiConstants.wifiAuthOpen,
          child: Text('开放'),
        ),
        DropdownMenuItem(
          value: BluFiConstants.wifiAuthWpaPsk,
          child: Text('WPA PSK'),
        ),
        DropdownMenuItem(
          value: BluFiConstants.wifiAuthWpa2Psk,
          child: Text('WPA2 PSK'),
        ),
        DropdownMenuItem(
          value: BluFiConstants.wifiAuthWpaWpa2Psk,
          child: Text('WPA/WPA2 PSK'),
        ),
      ],
      onChanged: (value) => widget.onSoftApAuthModeChanged(value!),
    );
  }

  /// 构建信道下拉框
  Widget _buildChannelDropdown() {
    return DropdownButtonFormField<int>(
      value: widget.softApChannel,
      decoration: const InputDecoration(
        labelText: '信道',
        border: OutlineInputBorder(),
      ),
      items: List.generate(13, (index) {
        final channel = index + 1;
        return DropdownMenuItem(
          value: channel,
          child: Text('信道 $channel'),
        );
      }),
      onChanged: (value) => widget.onSoftApChannelChanged(value!),
    );
  }

  /// 构建最大连接数下拉框
  Widget _buildMaxConnectionsDropdown() {
    return DropdownButtonFormField<int>(
      value: widget.softApMaxConnections,
      decoration: const InputDecoration(
        labelText: '最大连接数',
        border: OutlineInputBorder(),
      ),
      items: List.generate(10, (index) {
        final count = index + 1;
        return DropdownMenuItem(
          value: count,
          child: Text('$count 个设备'),
        );
      }),
      onChanged: (value) => widget.onSoftApMaxConnectionsChanged(value!),
    );
  }

  /// 是否应该显示Station配置
  bool _shouldShowStationConfig() {
    return widget.selectedMode == BluFiConstants.wifiModeSta ||
           widget.selectedMode == BluFiConstants.wifiModeApSta;
  }

  /// 是否应该显示SoftAP配置
  bool _shouldShowSoftApConfig() {
    return widget.selectedMode == BluFiConstants.wifiModeAp ||
           widget.selectedMode == BluFiConstants.wifiModeApSta;
  }
}
