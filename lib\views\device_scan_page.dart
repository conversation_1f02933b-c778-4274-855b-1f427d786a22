import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/blufi_device.dart';
import '../services/blufi_service.dart';
import '../viewmodels/device_config_viewmodel.dart';
import '../viewmodels/device_scan_viewmodel.dart';
import '../widgets/device_list_item.dart';
import '../widgets/status_indicator.dart';
import '../utils/constants.dart';
import 'blufi_device_page.dart';

/// 设备扫描页面
/// 显示可用的BluFi设备列表并支持连接
class DeviceScanPage extends StatefulWidget {
  const DeviceScanPage({Key? key}) : super(key: key);

  @override
  State<DeviceScanPage> createState() => _DeviceScanPageState();
}

class _DeviceScanPageState extends State<DeviceScanPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DeviceScanViewModel>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BluFi设备'),
        actions: [
          Consumer<DeviceScanViewModel>(
            builder: (context, viewModel, child) {
              return IconButton(
                onPressed:
                    viewModel.canScan ? () => _refreshDevices(viewModel) : null,
                icon: viewModel.isScanning
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh),
                tooltip: '刷新',
              );
            },
          ),
        ],
      ),
      body: Consumer<DeviceScanViewModel>(
        builder: (context, viewModel, child) {
          return FutureBuilder<bool>(
            future: viewModel.hasPermissions,
            builder: (context, permissionSnapshot) {
              if (permissionSnapshot.connectionState ==
                  ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final hasPermissions = permissionSnapshot.data ?? false;

              if (!hasPermissions) {
                return _buildPermissionRequest(viewModel);
              }

              return Column(
                children: [
                  _buildStatusBar(viewModel),
                  Expanded(
                    child: _buildDeviceList(viewModel),
                  ),
                ],
              );
            },
          );
        },
      ),
      floatingActionButton: Consumer<DeviceScanViewModel>(
        builder: (context, viewModel, child) {
          if (!viewModel.isInitialized) return const SizedBox.shrink();

          return FloatingActionButton.extended(
            onPressed: viewModel.isScanning
                ? () => viewModel.stopScan()
                : () => viewModel.startScan(),
            icon: Icon(viewModel.isScanning ? Icons.stop : Icons.search),
            label: Text(viewModel.isScanning ? '停止扫描' : '开始扫描'),
          );
        },
      ),
    );
  }

  /// 构建权限请求界面
  Widget _buildPermissionRequest(DeviceScanViewModel viewModel) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bluetooth_disabled,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              '需要蓝牙权限',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              '此应用需要蓝牙权限来扫描和连接ESP32设备',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton.icon(
              onPressed: () async {
                final granted = await viewModel.requestPermissions();
                if (granted) {
                  setState(() {}); // 刷新UI
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('权限被拒绝，无法使用BluFi功能'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              icon: const Icon(Icons.security),
              label: const Text('授予权限'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.largePadding,
                  vertical: AppConstants.defaultPadding,
                ),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextButton(
              onPressed: () async {
                final opened = await viewModel.openAppSettings();
                if (!opened && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('无法打开设置，请手动在系统设置中开启蓝牙权限'),
                    ),
                  );
                }
              },
              child: const Text('在设置中开启'),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            // 显示详细权限状态
            FutureBuilder<Map<String, String>>(
              future: viewModel.permissionStatus,
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  return ExpansionTile(
                    title: const Text('权限详情'),
                    children: snapshot.data!.entries.map((entry) {
                      return ListTile(
                        title: Text(entry.key),
                        trailing: Text(
                          entry.value,
                          style: TextStyle(
                            color: entry.value == '已授权'
                                ? Colors.green
                                : Colors.red,
                          ),
                        ),
                      );
                    }).toList(),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态栏
  Widget _buildStatusBar(DeviceScanViewModel viewModel) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Theme.of(context).primaryColor.withOpacity(0.1),
      child: Column(
        children: [
          Text(
            viewModel.scanStatusDescription,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          if (viewModel.errorMessage != null) ...[
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              viewModel.errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red[600],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建设备列表
  Widget _buildDeviceList(DeviceScanViewModel viewModel) {
    if (!viewModel.isInitialized) {
      return const LoadingIndicator(
        message: '正在初始化...',
      );
    }

    if (viewModel.errorMessage != null && !viewModel.hasDevices) {
      return ErrorIndicator(
        message: viewModel.errorMessage!,
        onRetry: () => _refreshDevices(viewModel),
        retryLabel: '重新扫描',
      );
    }

    if (viewModel.isScanning && !viewModel.hasDevices) {
      return const DeviceScanLoading();
    }

    if (!viewModel.hasDevices) {
      return EmptyDeviceList(
        onRefresh: () => _refreshDevices(viewModel),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _refreshDevices(viewModel),
      child: ListView.builder(
        itemCount: viewModel.devices.length,
        itemBuilder: (context, index) {
          final device = viewModel.devices[index];
          return DeviceListItem(
            device: device,
            isConnecting: false, // 不再显示连接状态，因为点击不会连接
            onTap: () => _navigateToDevicePage(device),
          );
        },
      ),
    );
  }

  /// 刷新设备列表
  Future<void> _refreshDevices(DeviceScanViewModel viewModel) async {
    await viewModel.refreshDevices();
  }

  /// 导航到设备页面（不连接设备）
  void _navigateToDevicePage(BluFiDevice device) {
    // 直接导航到BluFi设备页面，不进行连接
    // 连接操作由用户在设备页面手动点击Connect按钮触发
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (context) => DeviceConfigViewModel(
            Provider.of<BluFiService>(context, listen: false),
          ),
          child: BluFiDevicePage(device: device),
        ),
      ),
    );
  }
}

/// 设备扫描页面的Provider包装器
class DeviceScanPageProvider extends StatelessWidget {
  const DeviceScanPageProvider({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => DeviceScanViewModel(
        context.read(), // BluFiService
      ),
      child: const DeviceScanPage(),
    );
  }
}
