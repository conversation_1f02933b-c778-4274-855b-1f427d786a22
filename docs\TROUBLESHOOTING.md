# ESP32 BluFi 故障排除指南

本指南帮助您解决使用ESP32 BluFi Flutter应用时可能遇到的常见问题。

## 📋 目录

1. [连接问题](#连接问题)
2. [WiFi配置问题](#wifi配置问题)
3. [权限问题](#权限问题)
4. [性能问题](#性能问题)
5. [加密问题](#加密问题)
6. [调试技巧](#调试技巧)

## 🔗 连接问题

### 问题：无法发现BluFi设备

**症状**：
- 扫描不到任何设备
- 设备列表始终为空

**可能原因和解决方案**：

1. **蓝牙权限问题**
   ```dart
   // 检查权限状态
   final hasPermissions = await _viewModel.hasPermissions;
   if (!hasPermissions) {
     await _viewModel.requestPermissions();
   }
   ```

2. **蓝牙未开启**
   ```dart
   // 检查蓝牙状态
   final isEnabled = await _viewModel.isBluetoothEnabled;
   if (!isEnabled) {
     await _viewModel.enableBluetooth();
   }
   ```

3. **ESP32设备未进入BluFi模式**
   - 确保ESP32固件支持BluFi
   - 检查设备是否正确启动BluFi服务
   - 验证设备名称是否包含"BluFi"

4. **扫描时间过短**
   ```dart
   // 延长扫描时间
   await _viewModel.startScan();
   await Future.delayed(const Duration(seconds: 10)); // 增加到10秒
   await _viewModel.stopScan();
   ```

### 问题：设备连接失败

**症状**：
- 能发现设备但连接失败
- 连接超时

**解决方案**：

1. **检查设备距离**
   - 确保设备在蓝牙范围内（通常<10米）
   - 避免障碍物干扰

2. **重试连接**
   ```dart
   Future<bool> connectWithRetry(BluFiDevice device, {int maxRetries = 3}) async {
     for (int i = 0; i < maxRetries; i++) {
       try {
         final success = await _viewModel.connectToDevice(device);
         if (success) return true;
       } catch (e) {
         print('连接尝试 ${i + 1} 失败: $e');
         if (i < maxRetries - 1) {
           await Future.delayed(const Duration(seconds: 2));
         }
       }
     }
     return false;
   }
   ```

3. **清除蓝牙缓存**
   ```dart
   // 在连接前重置蓝牙状态
   await _blufiService.disconnect();
   await Future.delayed(const Duration(seconds: 1));
   ```

### 问题：连接后立即断开

**可能原因**：
- 设备电量不足
- 蓝牙干扰
- 固件问题

**解决方案**：
```dart
// 监听连接状态变化
_blufiService.connectionStateStream.listen((state) {
  if (state == ConnectionState.disconnected) {
    print('连接意外断开，尝试重连...');
    _attemptReconnection();
  }
});

Future<void> _attemptReconnection() async {
  await Future.delayed(const Duration(seconds: 2));
  // 重新连接逻辑
}
```

## 📶 WiFi配置问题

### 问题：WiFi配置发送失败

**症状**：
- 配置命令发送失败
- 设备无响应

**解决方案**：

1. **验证配置参数**
   ```dart
   final config = WiFiConfig.station(
     ssid: 'YourSSID',
     password: 'YourPassword',
   );
   
   if (!config.isValid) {
     throw Exception('WiFi配置参数无效');
   }
   ```

2. **检查SSID和密码**
   - 确保SSID存在且可连接
   - 验证密码正确性
   - 检查特殊字符是否正确编码

3. **分步发送配置**
   ```dart
   Future<bool> configureWiFiStepByStep(WiFiConfig config) async {
     try {
       // 1. 设置操作模式
       await _protocolService.setOpMode(config.mode);
       await Future.delayed(const Duration(milliseconds: 500));
       
       // 2. 设置SSID
       await _protocolService.setStaSsid(config.staSsid!);
       await Future.delayed(const Duration(milliseconds: 500));
       
       // 3. 设置密码
       await _protocolService.setStaPassword(config.staPassword!);
       await Future.delayed(const Duration(milliseconds, 500));
       
       // 4. 连接WiFi
       await _protocolService.connectWiFi();
       
       return true;
     } catch (e) {
       print('分步配置失败: $e');
       return false;
     }
   }
   ```

### 问题：WiFi连接超时

**症状**：
- 配置发送成功但WiFi未连接
- 长时间处于连接状态

**解决方案**：

1. **增加等待时间**
   ```dart
   Future<bool> waitForWiFiConnection({Duration timeout = const Duration(seconds: 30)}) async {
     final startTime = DateTime.now();
     
     while (DateTime.now().difference(startTime) < timeout) {
       final status = await _viewModel.getWiFiStatus();
       
       if (status?.isConnected == true) {
         return true;
       }
       
       await Future.delayed(const Duration(seconds: 2));
     }
     
     return false;
   }
   ```

2. **检查网络环境**
   - 确认WiFi网络正常工作
   - 检查信号强度
   - 验证网络认证方式

### 问题：企业级WiFi配置失败

**解决方案**：
```dart
Future<bool> configureEnterpriseWiFi() async {
  final config = WiFiConfig.enterprise(
    ssid: 'CorporateNetwork',
    username: '<EMAIL>',
    password: 'password',
  );
  
  // 添加证书（如果需要）
  if (caCertData != null) {
    await _protocolService.setCaCert(caCertData);
  }
  
  if (clientCertData != null) {
    await _protocolService.setClientCert(clientCertData);
  }
  
  return await _viewModel.configureWifi(config);
}
```

## 🔐 权限问题

### Android权限问题

**必需权限**：
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

<!-- Android 12+ -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
```

**运行时权限检查**：
```dart
Future<bool> checkAndRequestPermissions() async {
  final permissions = [
    Permission.bluetooth,
    Permission.bluetoothScan,
    Permission.bluetoothConnect,
    Permission.location,
  ];
  
  final statuses = await permissions.request();
  
  for (final permission in permissions) {
    if (statuses[permission] != PermissionStatus.granted) {
      print('权限被拒绝: $permission');
      return false;
    }
  }
  
  return true;
}
```

### iOS权限问题

**Info.plist配置**：
```xml
<!-- ios/Runner/Info.plist -->
<key>NSBluetoothAlwaysUsageDescription</key>
<string>此应用需要蓝牙权限来配置ESP32设备</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>此应用需要蓝牙权限来与ESP32设备通信</string>
```

## ⚡ 性能问题

### 问题：扫描速度慢

**优化方案**：
```dart
class OptimizedScanner {
  static const Duration _scanDuration = Duration(seconds: 5);
  static const Duration _scanInterval = Duration(milliseconds: 100);
  
  Future<List<BluFiDevice>> quickScan() async {
    final devices = <BluFiDevice>[];
    final completer = Completer<List<BluFiDevice>>();
    
    // 设置扫描过滤器
    await _blufiService.startScanWithFilter(
      serviceUuids: ['0000FFFF-0000-1000-8000-00805F9B34FB'], // BluFi服务UUID
      scanMode: ScanMode.lowLatency,
    );
    
    // 监听设备发现
    final subscription = _blufiService.scanResultStream.listen((device) {
      if (!devices.any((d) => d.id == device.id)) {
        devices.add(device);
      }
    });
    
    // 设置超时
    Timer(_scanDuration, () {
      subscription.cancel();
      _blufiService.stopScan();
      completer.complete(devices);
    });
    
    return completer.future;
  }
}
```

### 问题：内存使用过高

**优化方案**：
```dart
class MemoryOptimizer {
  static void optimizeMemoryUsage() {
    // 定期清理未使用的对象
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanupUnusedObjects();
    });
  }
  
  static void _cleanupUnusedObjects() {
    // 清理设备列表
    _deviceCache.removeWhere((device) => 
      DateTime.now().difference(device.lastSeen) > const Duration(minutes: 10)
    );
    
    // 清理日志
    if (_logBuffer.length > 1000) {
      _logBuffer.removeRange(0, _logBuffer.length - 500);
    }
  }
}
```

## 🔒 加密问题

### 问题：密钥协商失败

**调试方案**：
```dart
Future<bool> debugKeyNegotiation() async {
  try {
    // 1. 检查DH初始化
    final encService = EncryptionService();
    encService.initializeDH();
    
    if (!encService.isInitialized) {
      print('DH初始化失败');
      return false;
    }
    
    // 2. 获取公钥
    final publicKey = encService.getPublicKeyBytes();
    if (publicKey == null || publicKey.length != 128) {
      print('公钥生成失败或长度错误');
      return false;
    }
    
    print('公钥长度: ${publicKey.length}');
    print('公钥前8字节: ${publicKey.take(8).toList()}');
    
    // 3. 发送协商数据
    final negotiationFrame = await _protocolService.startKeyNegotiation();
    print('协商帧长度: ${negotiationFrame.length}');
    
    return true;
  } catch (e) {
    print('密钥协商调试失败: $e');
    return false;
  }
}
```

### 问题：加密数据损坏

**验证方案**：
```dart
Future<bool> testEncryption() async {
  final encService = EncryptionService();
  encService.initializeDH();
  
  // 模拟共享密钥
  final dummyPeerKey = Uint8List(128);
  for (int i = 0; i < 128; i++) {
    dummyPeerKey[i] = i % 256;
  }
  
  final hasSharedSecret = encService.computeSharedSecret(dummyPeerKey);
  if (!hasSharedSecret) {
    print('共享密钥计算失败');
    return false;
  }
  
  // 测试加密解密
  final testData = Uint8List.fromList('Hello BluFi!'.codeUnits);
  final encrypted = encService.encrypt(testData, 1);
  
  if (encrypted == null) {
    print('加密失败');
    return false;
  }
  
  final decrypted = encService.decrypt(encrypted, 1);
  
  if (decrypted == null || !_bytesEqual(decrypted, testData)) {
    print('解密失败或数据不匹配');
    return false;
  }
  
  print('加密测试通过');
  return true;
}

bool _bytesEqual(Uint8List a, Uint8List b) {
  if (a.length != b.length) return false;
  for (int i = 0; i < a.length; i++) {
    if (a[i] != b[i]) return false;
  }
  return true;
}
```

## 🐛 调试技巧

### 启用详细日志

```dart
class DebugHelper {
  static void enableVerboseLogging() {
    Logger.root.level = Level.ALL;
    Logger.root.onRecord.listen((record) {
      print('${record.level.name}: ${record.time}: ${record.message}');
      if (record.error != null) {
        print('错误: ${record.error}');
      }
      if (record.stackTrace != null) {
        print('堆栈: ${record.stackTrace}');
      }
    });
  }
  
  static void logFrameData(String direction, Uint8List data) {
    final hex = data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ');
    print('$direction: $hex');
  }
}
```

### 网络状态监控

```dart
class NetworkMonitor {
  static void startMonitoring() {
    Timer.periodic(const Duration(seconds: 5), (timer) async {
      final status = await _getNetworkStatus();
      print('网络状态: $status');
    });
  }
  
  static Future<String> _getNetworkStatus() async {
    // 实现网络状态检查
    return 'Connected';
  }
}
```

### 性能监控

```dart
class PerformanceMonitor {
  static final Map<String, Stopwatch> _timers = {};
  
  static void startTimer(String name) {
    _timers[name] = Stopwatch()..start();
  }
  
  static void endTimer(String name) {
    final timer = _timers[name];
    if (timer != null) {
      timer.stop();
      print('$name 耗时: ${timer.elapsedMilliseconds}ms');
      _timers.remove(name);
    }
  }
}

// 使用示例
PerformanceMonitor.startTimer('WiFi配置');
await _configureWiFi();
PerformanceMonitor.endTimer('WiFi配置');
```

## 📞 获取帮助

如果以上解决方案都无法解决您的问题，请：

1. **收集日志信息**：
   - 启用详细日志记录
   - 记录错误发生的具体步骤
   - 收集设备和环境信息

2. **检查已知问题**：
   - 查看项目的Issues页面
   - 搜索相关错误信息

3. **提交问题报告**：
   - 提供详细的错误描述
   - 包含日志信息和复现步骤
   - 说明设备型号和系统版本

4. **社区支持**：
   - 参与项目讨论
   - 查看文档和示例代码
   - 联系维护者获取帮助
