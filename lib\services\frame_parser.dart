import 'dart:typed_data';
import 'package:logger/logger.dart';
import '../models/blufi_frame.dart';
import '../models/connection_state.dart';
import '../utils/blufi_constants.dart';
import '../utils/extensions.dart';
import 'sequence_control_service.dart';

/// BluFi帧解析服务
class FrameParser {
  static final Logger _logger = Logger();

  /// 序列控制服务
  final SequenceControlService _sequenceControl = SequenceControlService();

  /// 分片缓存
  final Map<int, List<Uint8List>> _fragmentCache = {};

  /// 获取下一个序列号
  int getNextSequence() {
    return _sequenceControl.getNextSendSequence();
  }

  /// 重置序列号
  void resetSequence() {
    _sequenceControl.reset();
    _fragmentCache.clear();
    _logger.d('Sequence control reset');
  }

  /// 解析接收到的数据
  List<BluFiFrame> parseReceivedData(Uint8List data) {
    final List<BluFiFrame> frames = [];
    int offset = 0;

    while (offset < data.length) {
      try {
        final remainingData = data.sublist(offset);
        final frame = BluFiFrame.fromBytes(remainingData);

        // 验证校验和
        if (!frame.validateChecksum()) {
          _logger.w('Frame checksum validation failed');
          break;
        }

        // 验证序列号（防重放攻击）
        if (!_sequenceControl.validateReceiveSequence(frame.sequence)) {
          _logger.w('Frame sequence validation failed: ${frame.sequence}');
          // 根据ESP-IDF文档要求，序列号验证失败应该丢弃帧
          break;
        }

        frames.add(frame);

        // 计算下一帧的偏移量
        int frameLength = 4 + frame.data.length;
        if (frame.hasChecksum) frameLength += 2;
        offset += frameLength;
      } catch (e) {
        _logger.e('Error parsing frame at offset $offset: $e');
        break;
      }
    }

    return frames;
  }

  /// 处理分片帧
  Uint8List? handleFragmentedFrame(BluFiFrame frame) {
    final sequence = frame.sequence;

    // 如果不是分片帧，直接返回数据
    if (!frame.isFragment) {
      // 检查是否有之前的分片需要合并
      final fragments = _fragmentCache.remove(sequence);
      if (fragments != null) {
        // 这是最后一个分片，合并所有分片
        fragments.add(frame.data);
        return _combineFragments(fragments);
      }
      return frame.data;
    }

    // 初始化分片缓存
    if (!_fragmentCache.containsKey(sequence)) {
      _fragmentCache[sequence] = [];
    }

    // 添加当前分片
    _fragmentCache[sequence]!.add(frame.data);

    // 如果是分片帧，等待更多分片
    return null;
  }

  /// 合并分片数据
  Uint8List _combineFragments(List<Uint8List> fragments) {
    final totalLength =
        fragments.fold<int>(0, (sum, fragment) => sum + fragment.length);
    final combined = Uint8List(totalLength);
    int offset = 0;

    for (final fragment in fragments) {
      combined.setRange(offset, offset + fragment.length, fragment);
      offset += fragment.length;
    }

    return combined;
  }

  /// 创建设置安全模式的控制帧
  BluFiFrame createSetSecurityModeFrame({
    required bool controlFrameChecksum,
    required bool controlFrameEncrypt,
    required bool dataFrameChecksum,
    required bool dataFrameEncrypt,
  }) {
    int securityMode = 0;

    // 控制帧安全模式（高4位）
    if (controlFrameChecksum) securityMode |= 0x10;
    if (controlFrameEncrypt) securityMode |= 0x20;

    // 数据帧安全模式（低4位）
    if (dataFrameChecksum) securityMode |= 0x01;
    if (dataFrameEncrypt) securityMode |= 0x02;

    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeSetSecurityMode,
      sequence: getNextSequence(),
      data: Uint8List.fromList([securityMode]),
    );
  }

  /// 创建设置WiFi操作模式的控制帧
  BluFiFrame createSetOpModeFrame(int opMode) {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeSetOpMode,
      sequence: getNextSequence(),
      data: Uint8List.fromList([opMode]),
    );
  }

  /// 创建连接WiFi的控制帧
  BluFiFrame createConnectWifiFrame() {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeConnectWifi,
      sequence: getNextSequence(),
    );
  }

  /// 创建获取WiFi状态的控制帧
  BluFiFrame createGetWifiStatusFrame() {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeGetWifiStatus,
      sequence: getNextSequence(),
    );
  }

  /// 创建获取WiFi列表的控制帧
  BluFiFrame createGetWifiListFrame() {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeGetWifiList,
      sequence: getNextSequence(),
    );
  }

  /// 创建获取版本信息控制帧
  BluFiFrame createGetVersionFrame() {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeGetVersion,
      sequence: getNextSequence(),
    );
  }

  /// 创建断开WiFi连接的控制帧
  BluFiFrame createDisconnectWifiFrame() {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeDisconnectWifi,
      sequence: getNextSequence(),
    );
  }

  /// 创建断开STA设备的控制帧
  BluFiFrame createDeauthStaFrame(Uint8List staMac) {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeDeauthSta,
      sequence: getNextSequence(),
      data: staMac,
    );
  }

  /// 创建断开BLE连接的控制帧
  BluFiFrame createDisconnectBleFrame() {
    return BluFiFrame.control(
      subtype: BluFiConstants.ctrlSubtypeDisconnectBle,
      sequence: getNextSequence(),
    );
  }

  /// 创建STA SSID数据帧
  BluFiFrame createStaSsidFrame(String ssid,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeStaSsid,
      sequence: getNextSequence(),
      data: ssid.toUtf8Bytes(),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建STA密码数据帧
  BluFiFrame createStaPasswordFrame(String password,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeStaPassword,
      sequence: getNextSequence(),
      data: password.toUtf8Bytes(),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建SoftAP SSID数据帧
  BluFiFrame createSoftApSsidFrame(String ssid,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeSoftapSsid,
      sequence: getNextSequence(),
      data: ssid.toUtf8Bytes(),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建SoftAP密码数据帧
  BluFiFrame createSoftApPasswordFrame(String password,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeSoftapPassword,
      sequence: getNextSequence(),
      data: password.toUtf8Bytes(),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建SoftAP最大连接数数据帧
  BluFiFrame createSoftApMaxConnFrame(int maxConn,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeSoftapMaxConnNum,
      sequence: getNextSequence(),
      data: Uint8List.fromList([maxConn]),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建SoftAP认证模式数据帧
  BluFiFrame createSoftApAuthModeFrame(int authMode,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeSoftapAuthMode,
      sequence: getNextSequence(),
      data: Uint8List.fromList([authMode]),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建SoftAP信道数据帧
  BluFiFrame createSoftApChannelFrame(int channel,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeSoftapChannel,
      sequence: getNextSequence(),
      data: Uint8List.fromList([channel]),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建密钥协商数据帧
  BluFiFrame createNegotiationDataFrame(Uint8List publicKeyData,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeNegotiateData,
      sequence: getNextSequence(),
      data: publicKeyData,
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建STA BSSID数据帧
  BluFiFrame createStaBssidFrame(String bssid,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeStaBssid,
      sequence: getNextSequence(),
      data: bssid.toUtf8Bytes(),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建企业级WiFi用户名数据帧
  BluFiFrame createUsernameFrame(String username,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeUsername,
      sequence: getNextSequence(),
      data: username.toUtf8Bytes(),
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建CA证书数据帧
  BluFiFrame createCaCertFrame(Uint8List certData,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeCaCert,
      sequence: getNextSequence(),
      data: certData,
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建客户端证书数据帧
  BluFiFrame createClientCertFrame(Uint8List certData,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeClientCert,
      sequence: getNextSequence(),
      data: certData,
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建客户端私钥数据帧
  BluFiFrame createClientPrivateKeyFrame(Uint8List keyData,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeClientPrivateKey,
      sequence: getNextSequence(),
      data: keyData,
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 创建自定义数据帧
  BluFiFrame createCustomDataFrame(Uint8List customData,
      {bool encrypted = false, bool hasChecksum = false}) {
    return BluFiFrame.data(
      subtype: BluFiConstants.dataSubtypeCustomData,
      sequence: getNextSequence(),
      data: customData,
      encrypted: encrypted,
      hasChecksum: hasChecksum,
    );
  }

  /// 解析WiFi连接状态数据
  WiFiConnectionState? parseWifiConnectionState(Uint8List data) {
    if (data.isEmpty) return null;

    try {
      int offset = 0;
      final opMode = data[offset++];

      if (offset >= data.length) return null;
      final staState = data[offset++];

      if (offset >= data.length) return null;
      final softApConnCount = data[offset++];

      String? staSsid;
      String? staBssid;
      int? rssi;
      int? disconnectReason;
      int? maxReconnectTime;

      // 解析剩余数据（SSID/BSSID信息等）
      while (offset < data.length) {
        if (offset + 1 >= data.length) break;

        final infoLength = data[offset++];
        if (offset + infoLength > data.length) break;

        final infoData = data.sublist(offset, offset + infoLength);
        offset += infoLength;

        // 根据数据内容推断信息类型
        final infoString = String.fromCharCodes(infoData);
        if (infoString.contains(':') && infoString.length == 17) {
          // 可能是BSSID (MAC地址格式)
          staBssid = infoString;
        } else if (infoString.isNotEmpty && !infoString.contains('\x00')) {
          // 可能是SSID
          staSsid = infoString;
        }
      }

      return WiFiConnectionState(
        opMode: opMode,
        staState: staState,
        softApConnCount: softApConnCount,
        staSsid: staSsid,
        staBssid: staBssid,
        rssi: rssi,
        disconnectReason: disconnectReason,
        maxReconnectTime: maxReconnectTime,
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      _logger.e('Error parsing WiFi connection state: $e');
      return null;
    }
  }

  /// 清理分片缓存
  void clearFragmentCache() {
    _fragmentCache.clear();
  }

  /// 获取序列控制状态
  Map<String, dynamic> getSequenceStatistics() {
    return {
      'currentSendSequence': _sequenceControl.getCurrentSendSequence(),
    };
  }
}
