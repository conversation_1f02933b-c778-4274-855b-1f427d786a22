import 'dart:async';
import 'dart:io';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../services/bluetooth_service.dart';
import 'connection_cache.dart';
import 'connection_health_monitor.dart';

/// 连接问题诊断工具
/// 提供全面的连接问题诊断和修复建议
class ConnectionDiagnosticsTool {
  static final Logger _logger = Logger();
  
  /// 运行完整的连接诊断
  static Future<DiagnosticReport> runFullDiagnostics(
    BluetoothService bluetoothService,
    BluFiDevice? targetDevice,
  ) async {
    _logger.i('Starting full connection diagnostics');
    
    final report = DiagnosticReport();
    
    // 1. 系统环境检查
    report.systemCheck = await _checkSystemEnvironment();
    
    // 2. 蓝牙适配器检查
    report.adapterCheck = await _checkBluetoothAdapter();
    
    // 3. 权限检查
    report.permissionCheck = await _checkPermissions();
    
    // 4. 设备特定检查
    if (targetDevice != null) {
      report.deviceCheck = await _checkTargetDevice(targetDevice);
    }
    
    // 5. 连接缓存分析
    report.cacheAnalysis = _analyzeCacheData();
    
    // 6. 健康监控分析
    report.healthAnalysis = _analyzeHealthData();
    
    // 7. 生成修复建议
    report.recommendations = _generateRecommendations(report);
    
    // 8. 计算总体健康评分
    report.overallScore = _calculateOverallScore(report);
    
    _logger.i('Diagnostics completed with score: ${report.overallScore}/100');
    
    return report;
  }
  
  /// 检查系统环境
  static Future<SystemCheckResult> _checkSystemEnvironment() async {
    final result = SystemCheckResult();
    
    try {
      // 检查平台
      result.platform = Platform.operatingSystem;
      result.platformVersion = Platform.operatingSystemVersion;
      
      // 检查Flutter Blue Plus支持
      result.isBluetoothSupported = await fbp.FlutterBluePlus.isSupported;
      
      // 检查适配器名称
      try {
        result.adapterName = await fbp.FlutterBluePlus.adapterName;
      } catch (e) {
        result.adapterName = 'Unknown';
      }
      
      result.isHealthy = result.isBluetoothSupported;
      
    } catch (e) {
      result.error = e.toString();
      result.isHealthy = false;
    }
    
    return result;
  }
  
  /// 检查蓝牙适配器
  static Future<AdapterCheckResult> _checkBluetoothAdapter() async {
    final result = AdapterCheckResult();
    
    try {
      // 获取当前状态
      result.currentState = await fbp.FlutterBluePlus.adapterState.first
          .timeout(const Duration(seconds: 5));
      
      // 检查是否正在扫描
      result.isScanning = fbp.FlutterBluePlus.isScanningNow;
      
      // 获取已连接设备
      result.connectedDevices = fbp.FlutterBluePlus.connectedDevices.length;
      
      // 检查PHY支持（如果可用）
      try {
        final phySupport = await fbp.FlutterBluePlus.getPhySupport();
        result.phySupport = phySupport.toString();
      } catch (e) {
        result.phySupport = 'Not available';
      }
      
      result.isHealthy = result.currentState == fbp.BluetoothAdapterState.on;
      
    } catch (e) {
      result.error = e.toString();
      result.isHealthy = false;
    }
    
    return result;
  }
  
  /// 检查权限
  static Future<PermissionCheckResult> _checkPermissions() async {
    final result = PermissionCheckResult();
    
    try {
      // 这里应该检查具体的权限，但Flutter Blue Plus没有直接的权限检查API
      // 我们通过尝试操作来间接检查
      
      // 尝试获取适配器状态（需要基本蓝牙权限）
      try {
        await fbp.FlutterBluePlus.adapterState.first.timeout(const Duration(seconds: 2));
        result.hasBluetoothPermission = true;
      } catch (e) {
        result.hasBluetoothPermission = false;
        result.permissionErrors.add('Bluetooth permission: $e');
      }
      
      // 尝试开始扫描（需要扫描权限）
      try {
        await fbp.FlutterBluePlus.startScan(timeout: const Duration(seconds: 1));
        await fbp.FlutterBluePlus.stopScan();
        result.hasScanPermission = true;
      } catch (e) {
        result.hasScanPermission = false;
        result.permissionErrors.add('Scan permission: $e');
      }
      
      result.isHealthy = result.hasBluetoothPermission && result.hasScanPermission;
      
    } catch (e) {
      result.error = e.toString();
      result.isHealthy = false;
    }
    
    return result;
  }
  
  /// 检查目标设备
  static Future<DeviceCheckResult> _checkTargetDevice(BluFiDevice device) async {
    final result = DeviceCheckResult();
    
    try {
      result.deviceId = device.bluetoothDevice.remoteId.toString();
      result.deviceName = device.name;
      result.signalStrength = device.rssi;
      
      // 检查连接状态
      try {
        result.connectionState = await device.bluetoothDevice.connectionState.first
            .timeout(const Duration(seconds: 2));
      } catch (e) {
        result.connectionState = fbp.BluetoothConnectionState.disconnected;
      }
      
      // 检查信号强度
      result.hasGoodSignal = device.rssi > -80;
      
      // 检查设备是否在系统已知设备列表中
      final systemDevices = await fbp.FlutterBluePlus.systemDevices;
      result.isSystemKnown = systemDevices.any((d) => d.remoteId == device.bluetoothDevice.remoteId);
      
      result.isHealthy = result.hasGoodSignal && 
                        result.connectionState != fbp.BluetoothConnectionState.disconnected;
      
    } catch (e) {
      result.error = e.toString();
      result.isHealthy = false;
    }
    
    return result;
  }
  
  /// 分析缓存数据
  static CacheAnalysisResult _analyzeCacheData() {
    final result = CacheAnalysisResult();
    
    try {
      final stats = ConnectionStateCache.getStats();
      
      result.totalDevices = stats['totalDevices'] ?? 0;
      result.knownGoodDevices = stats['knownGoodDevices'] ?? 0;
      result.devicesWithFailures = stats['devicesWithFailures'] ?? 0;
      
      final avgTime = stats['averageConnectionTime'] as Duration?;
      result.averageConnectionTimeMs = avgTime?.inMilliseconds;
      
      // 计算成功率
      if (result.totalDevices > 0) {
        result.overallSuccessRate = result.knownGoodDevices / result.totalDevices;
      }
      
      result.isHealthy = result.overallSuccessRate > 0.7; // 70%成功率为健康
      
    } catch (e) {
      result.error = e.toString();
      result.isHealthy = false;
    }
    
    return result;
  }
  
  /// 分析健康监控数据
  static HealthAnalysisResult _analyzeHealthData() {
    final result = HealthAnalysisResult();
    
    try {
      final healthData = ConnectionHealthMonitor.instance.getAllDeviceHealth();
      
      result.monitoredDevices = healthData.length;
      
      if (healthData.isNotEmpty) {
        final scores = healthData.values.map((h) => h.healthScore).toList();
        result.averageHealthScore = scores.reduce((a, b) => a + b) / scores.length;
        
        result.healthyDevices = healthData.values.where((h) => h.healthScore > 70).length;
        result.unhealthyDevices = healthData.values.where((h) => h.healthScore <= 50).length;
      }
      
      result.isHealthy = result.averageHealthScore > 70;
      
    } catch (e) {
      result.error = e.toString();
      result.isHealthy = false;
    }
    
    return result;
  }
  
  /// 生成修复建议
  static List<String> _generateRecommendations(DiagnosticReport report) {
    final recommendations = <String>[];
    
    // 系统相关建议
    if (!report.systemCheck.isHealthy) {
      recommendations.add('检查设备蓝牙硬件支持');
      recommendations.add('更新系统蓝牙驱动');
    }
    
    // 适配器相关建议
    if (!report.adapterCheck.isHealthy) {
      recommendations.add('开启蓝牙适配器');
      recommendations.add('重启蓝牙服务');
    }
    
    // 权限相关建议
    if (!report.permissionCheck.isHealthy) {
      recommendations.add('检查应用蓝牙权限设置');
      recommendations.add('重新授权蓝牙和位置权限');
    }
    
    // 设备相关建议
    if (report.deviceCheck != null && !report.deviceCheck!.isHealthy) {
      recommendations.add('靠近目标设备以改善信号强度');
      recommendations.add('重启目标设备');
    }
    
    // 缓存相关建议
    if (!report.cacheAnalysis.isHealthy) {
      recommendations.add('清理连接缓存数据');
      recommendations.add('重置失败设备的连接记录');
    }
    
    // 健康监控相关建议
    if (!report.healthAnalysis.isHealthy) {
      recommendations.add('检查设备连接稳定性');
      recommendations.add('减少同时连接的设备数量');
    }
    
    // 通用建议
    if (recommendations.isEmpty) {
      recommendations.add('连接状态良好，继续监控');
    } else {
      recommendations.add('重启应用以应用修复');
      recommendations.add('如问题持续，请联系技术支持');
    }
    
    return recommendations;
  }
  
  /// 计算总体健康评分
  static double _calculateOverallScore(DiagnosticReport report) {
    double score = 0;
    int components = 0;
    
    // 系统检查 (20%)
    if (report.systemCheck.isHealthy) score += 20;
    components++;
    
    // 适配器检查 (25%)
    if (report.adapterCheck.isHealthy) score += 25;
    components++;
    
    // 权限检查 (20%)
    if (report.permissionCheck.isHealthy) score += 20;
    components++;
    
    // 设备检查 (15%)
    if (report.deviceCheck?.isHealthy ?? true) score += 15;
    components++;
    
    // 缓存分析 (10%)
    if (report.cacheAnalysis.isHealthy) score += 10;
    components++;
    
    // 健康分析 (10%)
    if (report.healthAnalysis.isHealthy) score += 10;
    components++;
    
    return score;
  }
}

/// 诊断报告
class DiagnosticReport {
  late SystemCheckResult systemCheck;
  late AdapterCheckResult adapterCheck;
  late PermissionCheckResult permissionCheck;
  DeviceCheckResult? deviceCheck;
  late CacheAnalysisResult cacheAnalysis;
  late HealthAnalysisResult healthAnalysis;
  List<String> recommendations = [];
  double overallScore = 0;
  
  String generateSummary() {
    final buffer = StringBuffer();
    buffer.writeln('=== Connection Diagnostics Report ===');
    buffer.writeln('Overall Score: ${overallScore.toStringAsFixed(1)}/100');
    buffer.writeln();
    
    buffer.writeln('System: ${systemCheck.isHealthy ? "✓" : "✗"} ${systemCheck.platform}');
    buffer.writeln('Adapter: ${adapterCheck.isHealthy ? "✓" : "✗"} ${adapterCheck.currentState}');
    buffer.writeln('Permissions: ${permissionCheck.isHealthy ? "✓" : "✗"}');
    
    if (deviceCheck != null) {
      buffer.writeln('Device: ${deviceCheck!.isHealthy ? "✓" : "✗"} ${deviceCheck!.deviceName}');
    }
    
    buffer.writeln('Cache: ${cacheAnalysis.isHealthy ? "✓" : "✗"} ${cacheAnalysis.totalDevices} devices');
    buffer.writeln('Health: ${healthAnalysis.isHealthy ? "✓" : "✗"} ${healthAnalysis.monitoredDevices} monitored');
    buffer.writeln();
    
    buffer.writeln('Recommendations:');
    for (int i = 0; i < recommendations.length; i++) {
      buffer.writeln('${i + 1}. ${recommendations[i]}');
    }
    
    buffer.writeln('=====================================');
    return buffer.toString();
  }
}

// 各种检查结果类
class SystemCheckResult {
  bool isHealthy = false;
  String? platform;
  String? platformVersion;
  bool isBluetoothSupported = false;
  String? adapterName;
  String? error;
}

class AdapterCheckResult {
  bool isHealthy = false;
  fbp.BluetoothAdapterState? currentState;
  bool isScanning = false;
  int connectedDevices = 0;
  String? phySupport;
  String? error;
}

class PermissionCheckResult {
  bool isHealthy = false;
  bool hasBluetoothPermission = false;
  bool hasScanPermission = false;
  List<String> permissionErrors = [];
  String? error;
}

class DeviceCheckResult {
  bool isHealthy = false;
  String? deviceId;
  String? deviceName;
  int signalStrength = -100;
  fbp.BluetoothConnectionState? connectionState;
  bool hasGoodSignal = false;
  bool isSystemKnown = false;
  String? error;
}

class CacheAnalysisResult {
  bool isHealthy = false;
  int totalDevices = 0;
  int knownGoodDevices = 0;
  int devicesWithFailures = 0;
  double overallSuccessRate = 0;
  int? averageConnectionTimeMs;
  String? error;
}

class HealthAnalysisResult {
  bool isHealthy = false;
  int monitoredDevices = 0;
  double averageHealthScore = 0;
  int healthyDevices = 0;
  int unhealthyDevices = 0;
  String? error;
}
