import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../services/blufi_service.dart';
import '../services/permission_service.dart';
import '../utils/constants.dart';

/// 设备扫描视图模型
/// 负责管理设备扫描相关的业务逻辑和状态
class DeviceScanViewModel extends ChangeNotifier {
  static final Logger _logger = Logger();

  final BluFiService _blufiService;
  final PermissionService _permissionService = PermissionService();

  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;

  // 设备列表
  List<BluFiDevice> _devices = [];

  // 错误信息
  String? _errorMessage;

  // 订阅管理
  StreamSubscription? _devicesSubscription;
  StreamSubscription? _stateSubscription;
  StreamSubscription? _errorSubscription;

  DeviceScanViewModel(this._blufiService);

  // Getters
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  List<BluFiDevice> get devices => List.unmodifiable(_devices);
  String? get errorMessage => _errorMessage;
  bool get hasDevices => _devices.isNotEmpty;
  bool get canScan => _isInitialized && !_isScanning;

  /// 检查是否有蓝牙权限
  Future<bool> get hasPermissions async {
    return await _permissionService.checkBluetoothPermissions();
  }

  /// 检查蓝牙是否已启用
  Future<bool> get isBluetoothEnabled async {
    // 这里可以添加蓝牙状态检查逻辑
    // 暂时返回true，实际应该检查蓝牙适配器状态
    return true;
  }

  /// 获取详细权限状态
  Future<Map<String, String>> get permissionStatus async {
    return await _permissionService.getDetailedPermissionStatus();
  }

  /// 请求蓝牙权限
  Future<bool> requestPermissions() async {
    try {
      final granted = await _permissionService.requestBluetoothPermissions();
      if (!granted) {
        _setError('蓝牙权限被拒绝，无法使用BluFi功能');
      }
      return granted;
    } catch (e) {
      _logger.e('Failed to request permissions: $e');
      _setError('权限请求失败: $e');
      return false;
    }
  }

  /// 启用蓝牙
  Future<void> enableBluetooth() async {
    try {
      // 这里可以添加启用蓝牙的逻辑
      // 在Android上可以通过Intent启动蓝牙设置
      _logger.i('Requesting to enable Bluetooth');
      // 实际实现需要平台特定代码
    } catch (e) {
      _logger.e('Failed to enable Bluetooth: $e');
      _setError('启用蓝牙失败: $e');
    }
  }

  /// 打开应用设置
  Future<bool> openAppSettings() async {
    try {
      return await _permissionService.openAppSettings();
    } catch (e) {
      _logger.e('Failed to open app settings: $e');
      _setError('打开设置失败: $e');
      return false;
    }
  }

  /// 初始化视图模型
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logger.i('Initializing device scan view model');

      // 初始化BluFi服务
      _isInitialized = await _blufiService.initialize();

      if (_isInitialized) {
        _setupEventListeners();
        _logger.i('Device scan view model initialized successfully');
      } else {
        _setError('服务初始化失败');
      }

      notifyListeners();
    } catch (e) {
      _logger.e('Failed to initialize device scan view model: $e');
      _setError('初始化失败: $e');
    }
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听设备发现
    _devicesSubscription = _blufiService.devicesStream.listen(
      (devices) {
        _devices = devices;
        _logger.d('Devices updated: ${devices.length} devices found');
        notifyListeners();
      },
      onError: (error) {
        _logger.e('Devices stream error: $error');
        _setError('设备扫描错误: $error');
      },
    );

    // 监听服务状态
    _stateSubscription = _blufiService.stateStream.listen(
      (state) {
        final wasScanning = _isScanning;
        _isScanning = state == BluFiServiceState.scanning;

        if (wasScanning != _isScanning) {
          _logger.d('Scanning state changed: $_isScanning');
          notifyListeners();
        }
      },
      onError: (error) {
        _logger.e('State stream error: $error');
        _setError('状态监听错误: $error');
      },
    );

    // 监听错误
    _errorSubscription = _blufiService.errorStream.listen(
      (error) {
        _logger.w('BluFi service error: $error');
        _setError(error);
      },
    );
  }

  /// 开始扫描设备
  Future<void> startScan() async {
    if (!canScan) {
      _logger.w(
          'Cannot start scan: isInitialized=$_isInitialized, isScanning=$_isScanning');
      return;
    }

    try {
      _logger.i('Starting device scan');
      _clearError();

      await _blufiService.startScan(
        timeout: const Duration(seconds: AppConstants.scanTimeoutSeconds),
      );
    } catch (e) {
      _logger.e('Failed to start scan: $e');
      _setError('开始扫描失败: $e');
    }
  }

  /// 停止扫描设备
  Future<void> stopScan() async {
    if (!_isScanning) return;

    try {
      _logger.i('Stopping device scan');
      await _blufiService.stopScan();
    } catch (e) {
      _logger.e('Failed to stop scan: $e');
      _setError('停止扫描失败: $e');
    }
  }

  /// 刷新设备列表
  Future<void> refreshDevices() async {
    try {
      _logger.i('Refreshing device list');
      _clearError();

      // 清除现有设备
      _blufiService.clearDiscoveredDevices();
      _devices.clear();
      notifyListeners();

      // 重新开始扫描
      await startScan();
    } catch (e) {
      _logger.e('Failed to refresh devices: $e');
      _setError('刷新设备列表失败: $e');
    }
  }

  /// 连接到设备
  Future<bool> connectToDevice(BluFiDevice device) async {
    if (_isScanning) {
      await stopScan();
    }

    try {
      _logger.i('Connecting to device: ${device.name}');
      _clearError();

      final success = await _blufiService.connectToDevice(device);

      if (success) {
        _logger.i('Successfully connected to device: ${device.name}');
      } else {
        _setError('连接设备失败');
      }

      return success;
    } catch (e) {
      _logger.e('Failed to connect to device: $e');
      _setError('连接设备失败: $e');
      return false;
    }
  }

  /// 获取设备信号强度描述
  String getDeviceSignalDescription(BluFiDevice device) {
    return device.rssiDescription;
  }

  /// 获取设备连接状态描述
  String getDeviceStatusDescription(BluFiDevice device) {
    if (device.isConnected) {
      return '已连接';
    } else {
      return '未连接';
    }
  }

  /// 检查设备是否可连接
  bool canConnectToDevice(BluFiDevice device) {
    return !_isScanning && device.isBluFiDevice;
  }

  /// 获取扫描状态描述
  String get scanStatusDescription {
    if (!_isInitialized) {
      return '正在初始化...';
    } else if (_isScanning) {
      return '正在扫描设备...';
    } else if (_devices.isEmpty) {
      return '未发现设备';
    } else {
      return '发现 ${_devices.length} 个设备';
    }
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// 获取按信号强度排序的设备列表
  List<BluFiDevice> get devicesSortedBySignal {
    final sortedDevices = List<BluFiDevice>.from(_devices);
    sortedDevices.sort((a, b) => b.rssi.compareTo(a.rssi));
    return sortedDevices;
  }

  /// 获取已连接的设备
  BluFiDevice? get connectedDevice {
    return _devices.where((device) => device.isConnected).firstOrNull;
  }

  /// 检查是否有已连接的设备
  bool get hasConnectedDevice => connectedDevice != null;

  /// 重置视图模型状态
  void reset() {
    _logger.i('Resetting device scan view model');

    stopScan();
    _devices.clear();
    _clearError();

    notifyListeners();
  }

  @override
  void dispose() {
    _logger.i('Disposing device scan view model');

    _devicesSubscription?.cancel();
    _stateSubscription?.cancel();
    _errorSubscription?.cancel();

    stopScan();

    super.dispose();
  }
}
