# 蓝牙连接故障排除指南

## 概述

本指南提供了全面的蓝牙连接故障排除方法，包括自动诊断工具和手动修复步骤。

## 🔧 自动诊断工具

### 1. 运行完整诊断

```dart
import 'package:your_app/utils/connection_diagnostics_tool.dart';

// 运行完整的连接诊断
final report = await ConnectionDiagnosticsTool.runFullDiagnostics(
  bluetoothService,
  targetDevice, // 可选，特定设备检查
);

// 查看诊断报告
print(report.generateSummary());

// 获取修复建议
for (final recommendation in report.recommendations) {
  print('建议: $recommendation');
}
```

### 2. 连接性能测试

```dart
import 'package:your_app/utils/connection_performance_test.dart';

// 运行性能测试
final testResult = await ConnectionPerformanceTest.runConnectionTest(
  bluetoothService,
  device,
  testRounds: 5,
);

// 查看测试结果
print('成功率: ${(testResult.successRate * 100).toStringAsFixed(1)}%');
print('平均连接时间: ${testResult.averageConnectionTime?.inMilliseconds}ms');
```

### 3. 健康监控

```dart
import 'package:your_app/utils/connection_health_monitor.dart';

// 获取健康报告
final healthReport = bluetoothService.getConnectionHealthReport();
print(healthReport);

// 获取特定设备健康状态
final deviceHealth = bluetoothService.getDeviceHealth(deviceId);
if (deviceHealth != null) {
  print('设备健康评分: ${deviceHealth['healthScore']}/100');
  print('稳定性评级: ${deviceHealth['stabilityRating']}');
}
```

## 🚨 常见连接失败问题及解决方案

### 1. 连接超时

**症状**: 连接尝试超过设定时间后失败

**可能原因**:
- 设备距离过远，信号弱
- 设备未处于可连接状态
- 蓝牙适配器繁忙

**解决方案**:
```dart
// 检查信号强度
if (device.rssi < -85) {
  print('信号太弱，请靠近设备');
}

// 增加超时时间（临时解决）
// 在 constants.dart 中调整
static const int connectionTimeoutSeconds = 8; // 增加到8秒
```

### 2. GATT错误 (错误133)

**症状**: 连接时出现GATT相关错误

**可能原因**:
- 设备蓝牙栈问题
- 连接状态不同步
- 系统蓝牙缓存问题

**解决方案**:
```dart
// 自动修复尝试
final analysis = await ConnectionFailureAnalyzer.analyzeFailure(
  device, 
  exception, 
  attemptDuration
);

final autoFixed = await ConnectionFailureAnalyzer.attemptAutoFix(
  analysis, 
  device
);

if (!autoFixed) {
  // 手动清理
  await device.bluetoothDevice.disconnect();
  await Future.delayed(Duration(seconds: 2));
  // 重新尝试连接
}
```

### 3. 权限问题

**症状**: 权限相关错误

**解决方案**:
```dart
// 检查权限状态
final report = await ConnectionDiagnosticsTool.runFullDiagnostics(
  bluetoothService, 
  null
);

if (!report.permissionCheck.isHealthy) {
  print('权限问题:');
  for (final error in report.permissionCheck.permissionErrors) {
    print('- $error');
  }
}
```

### 4. 设备已连接错误

**症状**: 尝试连接已连接的设备

**解决方案**:
```dart
// 检查当前连接状态
final currentState = await device.bluetoothDevice.connectionState.first;
if (currentState == BluetoothConnectionState.connected) {
  // 先断开再连接
  await device.bluetoothDevice.disconnect();
  await Future.delayed(Duration(seconds: 1));
}
```

## 📊 性能优化建议

### 1. 连接参数调优

根据实际测试结果调整连接参数：

```dart
// 在 constants.dart 中调整
class AppConstants {
  // 基于设备类型调整超时时间
  static const int connectionTimeoutSeconds = 5; // 稳定设备用5秒
  static const int fastConnectionTimeoutSeconds = 3; // 已知良好设备用3秒
  
  // 基于网络环境调整重试参数
  static const int connectionRetryDelayMs = 1000; // 干扰环境增加延迟
  static const int maxConnectionRetries = 3; // 增加重试次数
  
  // 基于设备能力调整信号要求
  static const int minSignalStrength = -80; // 调整最小信号强度
}
```

### 2. 缓存管理

定期清理和优化连接缓存：

```dart
// 定期清理过期缓存
bluetoothService.cleanupConnectionCache();

// 重置问题设备的缓存
bluetoothService.resetDeviceCache(problemDeviceId);

// 查看缓存统计
final stats = bluetoothService.getConnectionStats();
print('缓存统计: $stats');
```

### 3. 健康监控设置

启用持续的健康监控：

```dart
// 连接成功后自动启动监控
// (已在 BluetoothService 中自动实现)

// 定期检查健康状态
Timer.periodic(Duration(minutes: 5), (timer) {
  final healthReport = bluetoothService.getConnectionHealthReport();
  if (healthReport.contains('Critical') || healthReport.contains('Poor')) {
    print('检测到连接健康问题: $healthReport');
    // 采取修复措施
  }
});
```

## 🔍 调试技巧

### 1. 启用详细日志

```dart
// 设置日志级别
Logger.level = Level.debug;

// 查看连接过程详细信息
// 日志会显示：
// - 预连接检查结果
// - 连接尝试详情
// - 失败分析结果
// - 自动修复尝试
```

### 2. 使用诊断工具

```dart
// 在连接问题出现时立即运行诊断
if (!connectionSuccess) {
  final report = await ConnectionDiagnosticsTool.runFullDiagnostics(
    bluetoothService,
    device,
  );
  
  print('诊断评分: ${report.overallScore}/100');
  print('问题分析: ${report.generateSummary()}');
}
```

### 3. 性能基准测试

```dart
// 建立性能基准
final baselineTest = await ConnectionPerformanceTest.runConnectionTest(
  bluetoothService,
  device,
  testRounds: 10,
);

// 保存基准结果
final baseline = baselineTest.toMap();

// 后续比较性能变化
final currentTest = await ConnectionPerformanceTest.runConnectionTest(
  bluetoothService,
  device,
  testRounds: 5,
);

final comparison = ConnectionPerformanceTest.compareResults(
  baselineTest,
  currentTest,
);

print(comparison.generateReport());
```

## 📱 平台特定问题

### Android

**常见问题**:
- 权限问题（Android 12+需要精确位置权限）
- 蓝牙缓存问题
- 厂商定制系统兼容性

**解决方案**:
```dart
// Android特定的权限检查
if (Platform.isAndroid) {
  // 确保有必要的权限
  // 在 AndroidManifest.xml 中添加：
  // <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
  // <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
}
```

### iOS

**常见问题**:
- 后台连接限制
- 系统蓝牙状态延迟

**解决方案**:
```dart
// iOS特定的处理
if (Platform.isIOS) {
  // 等待蓝牙状态稳定
  if (await FlutterBluePlus.adapterState.first == BluetoothAdapterState.unknown) {
    await Future.delayed(Duration(seconds: 1));
  }
}
```

## 🆘 紧急修复步骤

当连接完全失败时，按以下顺序尝试：

1. **重启蓝牙适配器**
```dart
// Android可以尝试
if (Platform.isAndroid) {
  try {
    await FlutterBluePlus.turnOff();
    await Future.delayed(Duration(seconds: 2));
    await FlutterBluePlus.turnOn();
  } catch (e) {
    print('无法自动重启蓝牙: $e');
  }
}
```

2. **清理所有缓存**
```dart
bluetoothService.cleanupConnectionCache();
ConnectionStateCache.cleanup();
```

3. **重置健康监控**
```dart
ConnectionHealthMonitor.instance.dispose();
// 重新初始化会在下次连接时自动进行
```

4. **完全重启服务**
```dart
bluetoothService.dispose();
// 重新创建 BluetoothService 实例
```

## 📞 技术支持

如果以上方法都无法解决问题，请收集以下信息联系技术支持：

1. 诊断报告：`ConnectionDiagnosticsTool.runFullDiagnostics()`
2. 性能测试结果：`ConnectionPerformanceTest.runConnectionTest()`
3. 健康监控报告：`getConnectionHealthReport()`
4. 设备信息：平台、版本、蓝牙适配器型号
5. 目标设备信息：型号、固件版本、信号强度
