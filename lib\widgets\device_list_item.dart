import 'package:flutter/material.dart';
import '../models/blufi_device.dart';
import '../utils/constants.dart';

/// 设备列表项组件
/// 显示BluFi设备的基本信息和连接状态
class DeviceListItem extends StatelessWidget {
  final BluFiDevice device;
  final VoidCallback? onTap;
  final bool isConnecting;
  final bool showSignalStrength;

  const DeviceListItem({
    Key? key,
    required this.device,
    this.onTap,
    this.isConnecting = false,
    this.showSignalStrength = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding,
      ),
      child: ListTile(
        leading: _buildDeviceIcon(),
        title: Text(
          device.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              device.deviceId,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            if (showSignalStrength) ...[
              const SizedBox(height: 4),
              _buildSignalStrengthRow(context),
            ],
          ],
        ),
        trailing: _buildTrailing(context),
        onTap: isConnecting ? null : onTap,
        enabled: !isConnecting,
      ),
    );
  }

  /// 构建设备图标
  Widget _buildDeviceIcon() {
    IconData iconData;
    Color iconColor;

    if (device.isConnected) {
      iconData = Icons.bluetooth_connected;
      iconColor = Colors.green;
    } else if (isConnecting) {
      iconData = Icons.bluetooth_searching;
      iconColor = Colors.orange;
    } else {
      iconData = Icons.bluetooth;
      iconColor = Colors.blue;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withOpacity(0.1),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  /// 构建信号强度行
  Widget _buildSignalStrengthRow(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.signal_cellular_alt,
          size: 16,
          color: _getSignalColor(),
        ),
        const SizedBox(width: 4),
        Text(
          '${device.rssi} dBm (${device.rssiDescription})',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: _getSignalColor(),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建尾部组件
  Widget _buildTrailing(BuildContext context) {
    if (isConnecting) {
      return const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      );
    }

    if (device.isConnected) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          '已连接',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    return const Icon(Icons.chevron_right);
  }

  /// 获取信号强度颜色
  Color _getSignalColor() {
    if (device.rssi >= -50) return Colors.green;
    if (device.rssi >= -60) return Colors.lightGreen;
    if (device.rssi >= -70) return Colors.orange;
    if (device.rssi >= -80) return Colors.deepOrange;
    return Colors.red;
  }
}

/// 空设备列表组件
class EmptyDeviceList extends StatelessWidget {
  final String message;
  final VoidCallback? onRefresh;
  final bool showRefreshButton;

  const EmptyDeviceList({
    Key? key,
    this.message = '未发现设备',
    this.onRefresh,
    this.showRefreshButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bluetooth_disabled,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (showRefreshButton && onRefresh != null) ...[
              const SizedBox(height: AppConstants.largePadding),
              ElevatedButton.icon(
                onPressed: onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text('重新扫描'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 设备扫描加载组件
class DeviceScanLoading extends StatelessWidget {
  final String message;

  const DeviceScanLoading({
    Key? key,
    this.message = '正在扫描设备...',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
