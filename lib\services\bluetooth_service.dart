import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:logger/logger.dart';
import '../models/blufi_device.dart';
import '../utils/blufi_constants.dart';
import '../utils/constants.dart';
import '../utils/connection_cache.dart';
import '../utils/connection_failure_analyzer.dart';
import '../utils/connection_health_monitor.dart';

/// 蓝牙连接状态
enum BluetoothConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
}

/// 蓝牙服务类
/// 负责蓝牙设备的扫描、连接和GATT通信
class BluetoothService {
  static final Logger _logger = Logger();

  // 当前连接的设备
  BluFiDevice? _connectedDevice;
  fbp.BluetoothDevice? _bluetoothDevice;

  // GATT特征值
  fbp.BluetoothCharacteristic? _writeCharacteristic;
  fbp.BluetoothCharacteristic? _notifyCharacteristic;

  // 连接状态
  BluetoothConnectionState _connectionState =
      BluetoothConnectionState.disconnected;

  // 扫描相关
  StreamSubscription<List<fbp.ScanResult>>? _scanSubscription;
  final List<BluFiDevice> _discoveredDevices = [];

  // 设备连接状态监听
  StreamSubscription<fbp.BluetoothConnectionState>?
      _deviceConnectionSubscription;

  // 事件流控制器
  final StreamController<List<BluFiDevice>> _devicesController =
      StreamController<List<BluFiDevice>>.broadcast();
  final StreamController<BluetoothConnectionState> _connectionStateController =
      StreamController<BluetoothConnectionState>.broadcast();
  final StreamController<Uint8List> _dataReceivedController =
      StreamController<Uint8List>.broadcast();
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // 公开的流
  Stream<List<BluFiDevice>> get devicesStream => _devicesController.stream;
  Stream<BluetoothConnectionState> get connectionStateStream =>
      _connectionStateController.stream;
  Stream<Uint8List> get dataReceivedStream => _dataReceivedController.stream;
  Stream<String> get errorStream => _errorController.stream;

  /// 获取当前连接状态
  BluetoothConnectionState get connectionState => _connectionState;

  /// 获取当前连接的设备
  BluFiDevice? get connectedDevice => _connectedDevice;

  /// 初始化蓝牙服务
  Future<bool> initialize() async {
    try {
      // 检查蓝牙是否可用
      if (!await fbp.FlutterBluePlus.isSupported) {
        _logger.e('Bluetooth not available');
        _errorController.add('设备不支持蓝牙');
        return false;
      }

      // 检查蓝牙是否开启
      final adapterState = await fbp.FlutterBluePlus.adapterState.first;
      if (adapterState != fbp.BluetoothAdapterState.on) {
        _logger.w('Bluetooth is off');
        _errorController.add('请开启蓝牙');
        return false;
      }

      _logger.i('Bluetooth service initialized');
      return true;
    } catch (e) {
      _logger.e('Failed to initialize bluetooth service: $e');
      _errorController.add('蓝牙初始化失败: $e');
      return false;
    }
  }

  /// 开始扫描BluFi设备
  Future<void> startScan(
      {Duration timeout = const Duration(seconds: 10)}) async {
    if (_scanSubscription != null) {
      await stopScan();
    }

    _discoveredDevices.clear();
    _devicesController.add([]);

    try {
      _logger.i('Starting Bluetooth scan');

      // 开始扫描
      await fbp.FlutterBluePlus.startScan(
        timeout: timeout,
        withServices: [fbp.Guid(BluFiConstants.serviceUuid)],
      );

      // 监听扫描结果
      _scanSubscription = fbp.FlutterBluePlus.scanResults.listen(
        (results) {
          _processScanResults(results);
        },
        onError: (error) {
          _logger.e('Scan error: $error');
          _errorController.add('扫描错误: $error');
        },
      );

      // 设置扫描超时
      Timer(timeout, () async {
        await stopScan();
      });
    } catch (e) {
      _logger.e('Failed to start scan: $e');
      _errorController.add('开始扫描失败: $e');
    }
  }

  /// 停止扫描
  Future<void> stopScan() async {
    try {
      await fbp.FlutterBluePlus.stopScan();
      await _scanSubscription?.cancel();
      _scanSubscription = null;
      _logger.i('Bluetooth scan stopped');
    } catch (e) {
      _logger.e('Failed to stop scan: $e');
    }
  }

  /// 处理扫描结果
  void _processScanResults(List<fbp.ScanResult> results) {
    for (final result in results) {
      final device = BluFiDevice.fromScanResult(result);

      // 只添加BluFi设备
      if (device.isBluFiDevice) {
        final existingIndex = _discoveredDevices.indexWhere(
          (d) => d.deviceId == device.deviceId,
        );

        if (existingIndex >= 0) {
          // 更新现有设备的RSSI
          _discoveredDevices[existingIndex] = device;
        } else {
          // 添加新设备
          _discoveredDevices.add(device);
        }
      }
    }

    // 按信号强度排序
    _discoveredDevices.sort((a, b) => b.rssi.compareTo(a.rssi));
    _devicesController.add(List.from(_discoveredDevices));
  }

  /// 连接到设备 - 优化版本
  Future<bool> connectToDevice(BluFiDevice device) async {
    return await connectWithRetry(device);
  }

  /// 带重试机制的智能连接
  Future<bool> connectWithRetry(BluFiDevice device, {int? maxRetries}) async {
    final deviceId = device.bluetoothDevice.remoteId.toString();

    // 检查是否应该跳过连接（避免频繁重试）
    if (ConnectionStateCache.shouldSkipConnection(deviceId)) {
      _logger.w('Skipping connection attempt due to recent failure');
      return false;
    }

    final retries = maxRetries ?? AppConstants.maxConnectionRetries;
    final startTime = DateTime.now();

    // 记录连接尝试
    ConnectionStateCache.recordConnectionAttempt(deviceId);

    for (int attempt = 0; attempt < retries; attempt++) {
      try {
        if (attempt > 0) {
          _logger.i('Connection retry attempt ${attempt + 1}/$retries');
          // 重试前短暂延迟
          await Future.delayed(Duration(
              milliseconds: AppConstants.connectionRetryDelayMs * attempt));

          // 清理之前的连接状态
          await _cleanupPreviousConnection();
        }

        final success = await _attemptFastConnection(device);
        if (success) {
          final connectionTime = DateTime.now().difference(startTime);
          _logger.i(
              'Connection successful on attempt ${attempt + 1} in ${connectionTime.inMilliseconds}ms');

          // 记录成功连接
          ConnectionStateCache.recordConnectionSuccess(
              deviceId, connectionTime);

          // 启动健康监控
          ConnectionHealthMonitor.instance.startMonitoring(device);

          return true;
        }
      } catch (e) {
        final attemptDuration = DateTime.now().difference(startTime);
        _logger.w('Connection attempt ${attempt + 1} failed: $e');

        // 分析失败原因
        final analysis = await ConnectionFailureAnalyzer.analyzeFailure(
          device,
          e is Exception ? e : Exception(e.toString()),
          attemptDuration,
        );

        // 尝试自动修复
        final autoFixSuccess =
            await ConnectionFailureAnalyzer.attemptAutoFix(analysis, device);
        if (autoFixSuccess) {
          _logger.i('Auto-fix applied for ${analysis.failureType}');
        }

        if (attempt == retries - 1) {
          _logger.e('All connection attempts failed');
          _logger.e('Final failure analysis: ${analysis.summary}');
          _logger.e('Recommendations: ${analysis.recommendations.join(', ')}');

          // 记录连接失败
          ConnectionStateCache.recordConnectionFailure(deviceId, e.toString());

          _errorController.add(
              '连接设备失败: $e\n建议: ${analysis.recommendations.take(2).join(', ')}');
          _updateConnectionState(BluetoothConnectionState.disconnected);
          return false;
        }
      }
    }

    return false;
  }

  /// 快速连接尝试
  Future<bool> _attemptFastConnection(BluFiDevice device) async {
    if (_connectionState == BluetoothConnectionState.connecting ||
        _connectionState == BluetoothConnectionState.connected) {
      _logger.w('Already connecting or connected');
      return false;
    }

    final deviceId = device.bluetoothDevice.remoteId.toString();
    _updateConnectionState(BluetoothConnectionState.connecting);
    _logger.i('Fast connecting to device: ${device.name}');

    // 0. 预连接检查
    if (!await _preConnectionCheck(device)) {
      throw Exception('Pre-connection check failed');
    }

    // 1. 立即停止扫描以释放资源
    await stopScan();

    // 2. 确保设备未连接
    await _ensureDeviceDisconnected(device);

    // 3. 使用动态超时时间（基于历史性能）
    final recommendedTimeout =
        ConnectionStateCache.getRecommendedTimeout(deviceId);
    _logger.d(
        'Using timeout: ${recommendedTimeout.inMilliseconds}ms for device $deviceId');

    // 4. 使用优化的连接参数
    await device.bluetoothDevice.connect(
      autoConnect: false, // 明确禁用autoConnect以获得更快连接
      timeout: recommendedTimeout,
    );

    // 3. 连接已建立（connect返回即代表已连接，避免额外等待以提高速度）

    // 4. 立即设置设备引用
    _bluetoothDevice = device.bluetoothDevice;
    _connectedDevice = device;
    device.updateConnectionState(true);

    // 5. 并行执行服务发现和MTU优化
    final futures = await Future.wait([
      _discoverServices(),
      _optimizeMtu(),
    ]);

    if (!futures[0]) {
      await disconnect();
      return false;
    }

    // 6. 设置连接状态监听
    _setupConnectionStateListener();

    _logger.i('Successfully connected to device: ${device.name}');
    return true;
  }

  /// 设置连接状态监听
  void _setupConnectionStateListener() {
    _deviceConnectionSubscription?.cancel();
    _deviceConnectionSubscription =
        _connectedDevice!.bluetoothDevice.connectionState.listen(
      (fbpState) {
        _logger.i('Device connection state changed: $fbpState');

        // 将Flutter Blue Plus的状态映射到我们的状态
        BluetoothConnectionState? ourState;
        switch (fbpState) {
          case fbp.BluetoothConnectionState.connected:
            ourState = BluetoothConnectionState.connected;
            break;
          case fbp.BluetoothConnectionState.disconnected:
            ourState = BluetoothConnectionState.disconnected;
            break;
          // 忽略已弃用的connecting和disconnecting状态
          default:
            return; // 不处理其他状态
        }

        if (_connectionState != ourState) {
          if (ourState == BluetoothConnectionState.disconnected) {
            _logger.w('Device disconnected unexpectedly');
          }
          _updateConnectionState(ourState);
        }
      },
      onError: (error) {
        _logger.e('Device connection state error: $error');
      },
    );
  }

  /// 清理之前的连接状态
  Future<void> _cleanupPreviousConnection() async {
    try {
      _deviceConnectionSubscription?.cancel();
      _deviceConnectionSubscription = null;

      if (_bluetoothDevice != null) {
        await _bluetoothDevice!.disconnect();
      }

      _bluetoothDevice = null;
      _connectedDevice = null;
      _writeCharacteristic = null;
      _notifyCharacteristic = null;

      _logger.i('Previous connection cleaned up');
    } catch (e) {
      _logger.w('Error during connection cleanup: $e');
    }
  }

  /// 预连接检查
  Future<bool> _preConnectionCheck(BluFiDevice device) async {
    try {
      // 1. 检查蓝牙适配器状态
      final adapterState = await fbp.FlutterBluePlus.adapterState.first
          .timeout(const Duration(seconds: 2));

      if (adapterState != fbp.BluetoothAdapterState.on) {
        _logger.w('Bluetooth adapter not ready: $adapterState');
        return false;
      }

      // 2. 检查设备信号强度
      if (device.rssi < AppConstants.minSignalStrength) {
        _logger.w(
            'Device signal too weak: ${device.rssi} dBm (min: ${AppConstants.minSignalStrength})');
        return false;
      }

      // 3. 检查是否有其他设备正在连接
      final connectedDevices = fbp.FlutterBluePlus.connectedDevices;
      if (connectedDevices.length >= AppConstants.maxConcurrentConnections) {
        _logger.w(
            'Too many connected devices: ${connectedDevices.length} (max: ${AppConstants.maxConcurrentConnections})');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Pre-connection check failed: $e');
      return false;
    }
  }

  /// 确保设备已断开连接
  Future<void> _ensureDeviceDisconnected(BluFiDevice device) async {
    try {
      final currentState = await device.bluetoothDevice.connectionState.first
          .timeout(const Duration(seconds: 1));

      if (currentState == fbp.BluetoothConnectionState.connected) {
        _logger.i('Device already connected, disconnecting first');
        await device.bluetoothDevice.disconnect();

        // 等待断开完成
        await device.bluetoothDevice.connectionState
            .where(
                (state) => state == fbp.BluetoothConnectionState.disconnected)
            .timeout(const Duration(seconds: 3))
            .first;

        // 短暂延迟确保断开完成
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      _logger.w('Error ensuring device disconnected: $e');
      // 继续连接尝试
    }
  }

  /// 优化MTU设置
  Future<bool> _optimizeMtu() async {
    if (_bluetoothDevice == null) return true;

    try {
      // 在Android上请求优化的MTU大小
      if (Platform.isAndroid) {
        await _bluetoothDevice!.requestMtu(AppConstants.mtuRequestSize);
        _logger.i('MTU optimized to ${AppConstants.mtuRequestSize}');
      }
      return true;
    } catch (e) {
      _logger.w('MTU optimization failed: $e');
      // MTU优化失败不影响连接
      return true;
    }
  }

  /// 发现GATT服务
  Future<bool> _discoverServices() async {
    if (_bluetoothDevice == null) return false;

    try {
      _logger.i('Discovering GATT services');
      final services = await _bluetoothDevice!
          .discoverServices()
          .timeout(const Duration(seconds: 5)); // 添加服务发现超时

      // 查找BluFi服务（使用精确UUID匹配提高速度与稳定性）
      final targetServiceUuid = fbp.Guid(BluFiConstants.serviceUuid);
      final blufiService =
          services.where((s) => s.uuid == targetServiceUuid).toList();
      if (blufiService.isEmpty) {
        _logger.e('BluFi service not found');
        _errorController.add('未找到BluFi服务');
        return false;
      }
      final service = blufiService.first;

      // 查找特征值（使用精确UUID匹配）
      final writeUuid = fbp.Guid(BluFiConstants.writeCharacteristicUuid);
      final notifyUuid = fbp.Guid(BluFiConstants.notifyCharacteristicUuid);

      for (final characteristic in service.characteristics) {
        if (characteristic.uuid == writeUuid) {
          _writeCharacteristic = characteristic;
          _logger.d('Found write characteristic');
        } else if (characteristic.uuid == notifyUuid) {
          _notifyCharacteristic = characteristic;
          _logger.d('Found notify characteristic');
        }
      }

      if (_writeCharacteristic == null || _notifyCharacteristic == null) {
        _logger.e('Required characteristics not found');
        _errorController.add('未找到必需的特征值');
        return false;
      }

      // 先监听再启用通知，避免丢包
      _notifyCharacteristic!.lastValueStream.listen(
        (data) {
          if (data.isNotEmpty) {
            _dataReceivedController.add(Uint8List.fromList(data));
          }
        },
        onError: (error) {
          _logger.e('Notification error: $error');
          _errorController.add('数据接收错误: $error');
        },
      );

      // 启用通知
      await _notifyCharacteristic!.setNotifyValue(true);

      _logger.i('GATT services discovered successfully');
      return true;
    } catch (e) {
      _logger.e('Failed to discover services: $e');
      _errorController.add('服务发现失败: $e');
      return false;
    }
  }

  /// 发送数据
  Future<bool> sendData(Uint8List data) async {
    if (_writeCharacteristic == null ||
        _connectionState != BluetoothConnectionState.connected) {
      _logger.w('Not connected or write characteristic not available');
      return false;
    }

    try {
      // 优先使用当前MTU优化分包大小（iOS自动，Android手动申请过）
      int mtu = _bluetoothDevice?.mtuNow ?? 23; // 如果可用，使用当前MTU
      final maxPacketSize = (mtu - 3).clamp(20, AppConstants.mtuRequestSize);

      for (int i = 0; i < data.length; i += maxPacketSize) {
        final end =
            (i + maxPacketSize < data.length) ? i + maxPacketSize : data.length;
        final packet = data.sublist(i, end);

        await _writeCharacteristic!.write(packet, withoutResponse: false);

        // 自适应节流：仅在小MTU或长数据时延迟
        if (end < data.length && (mtu <= 23 || data.length > 100)) {
          await Future.delayed(const Duration(milliseconds: 5));
        }
      }

      _logger.d('Data sent successfully: ${data.length} bytes');
      return true;
    } catch (e) {
      _logger.e('Failed to send data: $e');
      _errorController.add('数据发送失败: $e');
      return false;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    if (_connectionState == BluetoothConnectionState.disconnected) {
      return;
    }

    try {
      _updateConnectionState(BluetoothConnectionState.disconnecting);
      _logger.i('Disconnecting from device');

      // 禁用通知
      if (_notifyCharacteristic != null) {
        try {
          await _notifyCharacteristic!.setNotifyValue(false);
        } catch (e) {
          _logger.w('Failed to disable notifications: $e');
        }
      }

      // 断开连接
      if (_bluetoothDevice != null) {
        await _bluetoothDevice!.disconnect();
      }

      // 停止健康监控
      if (_connectedDevice != null) {
        final deviceId = _connectedDevice!.bluetoothDevice.remoteId.toString();
        ConnectionHealthMonitor.instance.stopMonitoring(deviceId);
      }

      // 清理状态
      _bluetoothDevice = null;
      _writeCharacteristic = null;
      _notifyCharacteristic = null;
      _connectedDevice?.updateConnectionState(false);
      _connectedDevice = null;

      // 取消设备连接状态监听
      _deviceConnectionSubscription?.cancel();
      _deviceConnectionSubscription = null;

      _updateConnectionState(BluetoothConnectionState.disconnected);
      _logger.i('Device disconnected');
    } catch (e) {
      _logger.e('Failed to disconnect: $e');
      _errorController.add('断开连接失败: $e');
      _updateConnectionState(BluetoothConnectionState.disconnected);
    }
  }

  /// 更新连接状态
  void _updateConnectionState(BluetoothConnectionState newState) {
    if (_connectionState != newState) {
      _connectionState = newState;
      _connectionStateController.add(newState);
      _logger.d('Connection state changed to: $newState');
    }
  }

  /// 获取已发现的设备列表
  List<BluFiDevice> get discoveredDevices => List.from(_discoveredDevices);

  /// 清除已发现的设备
  void clearDiscoveredDevices() {
    _discoveredDevices.clear();
    _devicesController.add([]);
  }

  /// 获取连接性能统计
  Map<String, dynamic> getConnectionStats() {
    final stats = ConnectionStateCache.getStats();
    stats['currentConnectionState'] = _connectionState.toString();
    stats['connectedDevice'] = _connectedDevice?.name ?? 'None';
    return stats;
  }

  /// 重置设备连接缓存
  void resetDeviceCache(String deviceId) {
    ConnectionStateCache.resetDevice(deviceId);
    _logger.i('Reset connection cache for device: $deviceId');
  }

  /// 清理过期的连接缓存
  void cleanupConnectionCache() {
    ConnectionStateCache.cleanup();
    _logger.i('Connection cache cleanup completed');
  }

  /// 获取连接健康报告
  String getConnectionHealthReport() {
    return ConnectionHealthMonitor.instance.generateHealthReport();
  }

  /// 获取设备健康状态
  Map<String, dynamic>? getDeviceHealth(String deviceId) {
    final health = ConnectionHealthMonitor.instance.getDeviceHealth(deviceId);
    return health?.toMap();
  }

  /// 释放资源
  void dispose() {
    stopScan();
    disconnect();
    _devicesController.close();
    _connectionStateController.close();
    _dataReceivedController.close();
    _errorController.close();
  }
}
