import 'dart:typed_data';

/// 企业级WiFi配置模型
/// 支持WPA2-Enterprise等高级认证方式
class EnterpriseWiFiConfig {
  /// SSID
  final String ssid;

  /// 用户名
  final String username;

  /// 密码
  final String password;

  /// CA证书数据
  final Uint8List? caCert;

  /// 客户端证书数据
  final Uint8List? clientCert;

  /// 客户端私钥数据
  final Uint8List? clientPrivateKey;

  /// 服务器证书数据（可选）
  final Uint8List? serverCert;

  /// 服务器私钥数据（可选）
  final Uint8List? serverPrivateKey;

  /// 认证方法
  final EnterpriseAuthMethod authMethod;

  /// 是否验证服务器证书
  final bool validateServerCert;

  /// 匿名身份（用于外层认证）
  final String? anonymousIdentity;

  const EnterpriseWiFiConfig({
    required this.ssid,
    required this.username,
    required this.password,
    this.caCert,
    this.clientCert,
    this.clientPrivateKey,
    this.serverCert,
    this.serverPrivateKey,
    this.authMethod = EnterpriseAuthMethod.peap,
    this.validateServerCert = true,
    this.anonymousIdentity,
  });

  /// 检查配置是否有效
  bool get isValid {
    if (ssid.isEmpty || username.isEmpty || password.isEmpty) {
      return false;
    }

    // 根据认证方法检查必需的证书
    switch (authMethod) {
      case EnterpriseAuthMethod.tls:
        return clientCert != null && clientPrivateKey != null;
      case EnterpriseAuthMethod.peap:
      case EnterpriseAuthMethod.ttls:
        return true; // PEAP和TTLS只需要用户名密码
    }
  }

  /// 获取配置描述
  String get description {
    return 'Enterprise WiFi: $ssid (${authMethod.displayName})';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'ssid': ssid,
      'username': username,
      'password': password,
      'authMethod': authMethod.name,
      'validateServerCert': validateServerCert,
      'anonymousIdentity': anonymousIdentity,
      'hasCaCert': caCert != null,
      'hasClientCert': clientCert != null,
      'hasClientPrivateKey': clientPrivateKey != null,
      'hasServerCert': serverCert != null,
      'hasServerPrivateKey': serverPrivateKey != null,
    };
  }

  /// 从JSON创建
  factory EnterpriseWiFiConfig.fromJson(Map<String, dynamic> json) {
    return EnterpriseWiFiConfig(
      ssid: json['ssid'] ?? '',
      username: json['username'] ?? '',
      password: json['password'] ?? '',
      authMethod: EnterpriseAuthMethod.values.firstWhere(
        (method) => method.name == json['authMethod'],
        orElse: () => EnterpriseAuthMethod.peap,
      ),
      validateServerCert: json['validateServerCert'] ?? true,
      anonymousIdentity: json['anonymousIdentity'],
    );
  }

  /// 复制并修改
  EnterpriseWiFiConfig copyWith({
    String? ssid,
    String? username,
    String? password,
    Uint8List? caCert,
    Uint8List? clientCert,
    Uint8List? clientPrivateKey,
    Uint8List? serverCert,
    Uint8List? serverPrivateKey,
    EnterpriseAuthMethod? authMethod,
    bool? validateServerCert,
    String? anonymousIdentity,
  }) {
    return EnterpriseWiFiConfig(
      ssid: ssid ?? this.ssid,
      username: username ?? this.username,
      password: password ?? this.password,
      caCert: caCert ?? this.caCert,
      clientCert: clientCert ?? this.clientCert,
      clientPrivateKey: clientPrivateKey ?? this.clientPrivateKey,
      serverCert: serverCert ?? this.serverCert,
      serverPrivateKey: serverPrivateKey ?? this.serverPrivateKey,
      authMethod: authMethod ?? this.authMethod,
      validateServerCert: validateServerCert ?? this.validateServerCert,
      anonymousIdentity: anonymousIdentity ?? this.anonymousIdentity,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnterpriseWiFiConfig &&
        other.ssid == ssid &&
        other.username == username &&
        other.password == password &&
        other.authMethod == authMethod &&
        other.validateServerCert == validateServerCert &&
        other.anonymousIdentity == anonymousIdentity;
  }

  @override
  int get hashCode {
    return Object.hash(
      ssid,
      username,
      password,
      authMethod,
      validateServerCert,
      anonymousIdentity,
    );
  }

  @override
  String toString() {
    return 'EnterpriseWiFiConfig{ssid: $ssid, username: $username, authMethod: $authMethod}';
  }
}

/// 企业级认证方法枚举
enum EnterpriseAuthMethod {
  /// EAP-TLS (需要客户端证书)
  tls,
  
  /// PEAP (Protected EAP)
  peap,
  
  /// EAP-TTLS (Tunneled TLS)
  ttls,
}

/// 企业级认证方法扩展
extension EnterpriseAuthMethodExtension on EnterpriseAuthMethod {
  /// 显示名称
  String get displayName {
    switch (this) {
      case EnterpriseAuthMethod.tls:
        return 'EAP-TLS';
      case EnterpriseAuthMethod.peap:
        return 'PEAP';
      case EnterpriseAuthMethod.ttls:
        return 'EAP-TTLS';
    }
  }

  /// 描述
  String get description {
    switch (this) {
      case EnterpriseAuthMethod.tls:
        return '使用客户端证书进行认证，安全性最高';
      case EnterpriseAuthMethod.peap:
        return '使用用户名密码认证，通过TLS隧道保护';
      case EnterpriseAuthMethod.ttls:
        return '隧道式TLS认证，支持多种内层认证方法';
    }
  }

  /// 是否需要客户端证书
  bool get requiresClientCert {
    return this == EnterpriseAuthMethod.tls;
  }

  /// 是否需要CA证书
  bool get requiresCaCert {
    return true; // 所有企业级认证都建议使用CA证书
  }
}

/// 证书类型枚举
enum CertificateType {
  ca,
  client,
  server,
  clientPrivateKey,
  serverPrivateKey,
}

/// 证书类型扩展
extension CertificateTypeExtension on CertificateType {
  /// 显示名称
  String get displayName {
    switch (this) {
      case CertificateType.ca:
        return 'CA证书';
      case CertificateType.client:
        return '客户端证书';
      case CertificateType.server:
        return '服务器证书';
      case CertificateType.clientPrivateKey:
        return '客户端私钥';
      case CertificateType.serverPrivateKey:
        return '服务器私钥';
    }
  }

  /// 文件扩展名
  List<String> get fileExtensions {
    switch (this) {
      case CertificateType.ca:
      case CertificateType.client:
      case CertificateType.server:
        return ['crt', 'cer', 'pem', 'der'];
      case CertificateType.clientPrivateKey:
      case CertificateType.serverPrivateKey:
        return ['key', 'pem', 'p12', 'pfx'];
    }
  }
}
